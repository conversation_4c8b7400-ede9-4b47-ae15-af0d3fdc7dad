﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using BIOME.Services;
using BIOME.ViewModels;
using BIOME.Models;
using BIOMEWebApplication.Areas.Admin.Constants;
using BIOMEWebApplication.Extensions;
using BIOMEWebApplication.ModelState;
using BIOME.Constants;
using System.Web.Mvc;
using Microsoft.AspNet.Identity;
using MvcPaging;
using System.Globalization;
using BIOMEWebApplication.Authorization;
using System.Reflection;

namespace BIOMEWebApplication.Areas.Admin.Controllers
{
#if INTERNET
    [AccessDeniedAuthorize(Roles = UserRoles.FullNoAccess)]
#elif INTRANET
    [AccessDeniedAuthorize(Roles = UserRoles.SystemAdmin)]
#endif
    [RoutePrefix(ControllerName.AuditTrail)]
    public class AuditTrailController : AdminControllerBase
    {
        #region Fields

        private IAuditTrailService auditTrailService;
        private IUserService userService;
        private ISystemParametersService systemParametersService;

        #endregion

        #region Constructors

        public AuditTrailController(IAuditTrailService auditTrailService, ISystemParametersService systemParametersService, IPageService pageService, IUserService userService) : base(pageService, userService)
        {
            this.auditTrailService = auditTrailService;
            this.userService = userService;
            this.systemParametersService = systemParametersService;

            ViewBag.MainMenu = BIOME.Enumerations.Menu.Admin.MainMenu.AuditTrails;
        }

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            base.Initialize(requestContext);

            BreadcrumbsList.Add(new Breadcrumb() { Name = "Audit Trail", UrlRootPath = Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Login, page = 1 }) });
        }

        #endregion

        #region Public Methods

        // GET: Admin/AuditTrailPage
        [Route("AuditTrailPage/{actionid}/{page}", Name = AuditTrailControllerRoute.GetAuditTrailPage)]
        public ActionResult AuditTrailPage(int actionid, int page, string Email = "", string PeriodFrom = "", string PeriodTo = "", string ipAddress = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
            int paginationSize = systemParametersService.GetPaginationPageSize();
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }

            //List<AuditTrailLogging> auditTrailList = auditTrailService.GetListAuditTrail(actionid, Email, tPeriodFrom, tPeriodTo,ipAddress);
            IPagedList<AuditTrailLogging> pagedItems = auditTrailService.GetListAuditTrail(actionid, Email, tPeriodFrom, tPeriodTo, ipAddress, page - 1, paginationSize);
            if (actionid == (int)BIOME.Enumerations.Audit.Action.Login)
            {
                ViewBag.Title = "Audit Trail Login";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Login;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Login", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Login, page = 1 }) });
                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Login, "");
            }
            else if (actionid == (int)BIOME.Enumerations.Audit.Action.Logout)
            {
                ViewBag.Title = "Audit Trail Logout";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Logout;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Logout", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Logout, page = 1 }) });
                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Logout, "");
            }
            else if (actionid == (int)BIOME.Enumerations.Audit.Action.AccessResourcesUnsuccessful)
            {
                ViewBag.Title = "Audit Trail Access Resources";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.AccessResourcesUnsuccessful;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Access Resources", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.AccessResourcesUnsuccessful, page = 1 }) });
                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AccessResources, "");
            }
            else if (actionid == (int)BIOME.Enumerations.Audit.Action.SystemStartUpShutDown)
            {
                ViewBag.Title = "Audit Trail Startup/Shutdown";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.SystemStartUpShutDown;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Startup/Shutdown", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.SystemStartUpShutDown, page = 1 }) });
                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Startup, "");
            }
            else if (actionid == (int)BIOME.Enumerations.Audit.Action.Transactions)
            {
                ViewBag.Title = "Audit Trail Transactions";
                ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Transactions;
                ConstructBreadcrumbs(new Breadcrumb() { Name = "Transactions", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailPage, new { actionid = (int)BIOME.Enumerations.Audit.Action.Transactions, page = 1 }) });
                if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                    auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_Transactions, "");
            }

            //IPagedList<AuditTrailLogging> pagedItems = auditTrailList.ToPagedList<AuditTrailLogging>(page - 1, paginationSize);
            ViewBag.pagedItems = pagedItems;
            ViewBag.varpage = page;
            ViewBag.actionid = actionid;

            AuditTrailViewModel.SearchViewModel searchVM = new AuditTrailViewModel.SearchViewModel();
            searchVM.Email = Email;
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            
            return View(AuditTrailView.AuditTrailPage, searchVM);
        }

        // GET: Admin/AuditTrailListing
        [Route("AuditTrailListing/{page}", Name = AuditTrailControllerRoute.GetAuditTrailListing)]
        public ActionResult AuditTrailListing(int page, string module = "--All--", string Email = "", string PeriodFrom = "", string PeriodTo = "", string ipAddress = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
            int paginationSize = systemParametersService.GetPaginationPageSize();
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }

            //List<AuditTrailLogging> auditTrailList = auditTrailService.GetAuditTrails(module, Email, tPeriodFrom, tPeriodTo, ipAddress);
            IPagedList<AuditTrailLogging> pagedItems = auditTrailService.GetAuditTrails(module, Email, tPeriodFrom, tPeriodTo, ipAddress, page - 1, paginationSize);

            ViewBag.Title = "Audit Log";
            ViewBag.AuditTrailMenu = BIOME.Enumerations.Menu.Admin.AuditTrailsSubMenu.Transactions;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Audit Log Listing", UrlRootPath = @Url.RouteUrl(AuditTrailControllerRoute.GetAuditTrailListing, new { actionid = (int)BIOME.Enumerations.Audit.Action.Transactions, page = 1 }) });
            if (HttpContext.Request.HttpMethod == "GET" && page == 1)
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AuditLogListing, "");

            //IPagedList<AuditTrailLogging> pagedItems = auditTrailList.ToPagedList<AuditTrailLogging>(page - 1, paginationSize);
            ViewBag.pagedItems = pagedItems;
            ViewBag.varpage = page;
            ViewBag.actionid = 0;
#if (DEBUG || UAT)
            ViewBag.UAT = true;
#endif

            List<string> list = new List<string>();
            foreach (FieldInfo finfo in typeof(BIOME.Enumerations.Audit.Module).GetFields())
            {
                list.Add(finfo.GetValue(null).ToString());
            }
            list.Sort();
            list.Insert(0, "--All--");
            ViewBag.moduleList = list;

            AuditTrailViewModel.SearchViewModel searchVM = new AuditTrailViewModel.SearchViewModel();
            searchVM.module = module;
            searchVM.Email = Email;
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            return View(AuditTrailView.AuditTrail, searchVM);
        }


        [AjaxAuthorization]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult SendAuditLogReport()
        {
            Hangfire.BackgroundJob.Enqueue<IBatchJobService>(batch => batch.SendAuditLogMonthlyReport(true));
            return Json(true);
        }

        // Post: Admin/MaintainableListPage
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("PostSearch/{actionid}/{page}", Name = AuditTrailControllerRoute.PostSearch)]
        public ActionResult PostSearch(int actionid, int page, AuditTrailViewModel.SearchViewModel searchVM)
        {
            return AuditTrailPage(actionid, 1, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo,searchVM.ipAddress);
        }

        // Post: Admin/AuditTrailListing
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("PostAuditTrailListing/{page}", Name = AuditTrailControllerRoute.PostAuditTrailListing)]
        public ActionResult PostAuditTrailListing(int page, AuditTrailViewModel.SearchViewModel searchVM)
        {
            page = 1;
            return AuditTrailListing(page, searchVM.module, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo, searchVM.ipAddress);
        }

        // GET: Admin/AuditTrailPage
        [Route("AuditLogPage/{actionid}/{page}", Name = AuditTrailControllerRoute.GetAuditLogPage)]
        public ActionResult AuditLogPage(  int page, string Email = "", string PeriodFrom = "", string PeriodTo = "", string ModuleName = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            int auditLogsPeriod = systemParametersService.GetAuditLogsStoragePeriod();
            int paginationSize = systemParametersService.GetPaginationPageSize();
            List<AuditLog> auditTrailList = new List<AuditLog>();
            List<AuditTrailViewModel.AuditlogViewModel> viewauditTrailList = new List<AuditTrailViewModel.AuditlogViewModel>();
            auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.AuditTrail, BIOME.Enumerations.Audit.AuditLogAction.View_AuditDetails, "");

            //            if (!string.IsNullOrEmpty(Email) || !string.IsNullOrEmpty(PeriodFrom) || !string.IsNullOrEmpty(PeriodTo) || !string.IsNullOrEmpty(ModuleName))
            //            {
            //                DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
            //                if (!string.IsNullOrEmpty(PeriodFrom))
            //                {
            //                    tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            //                }

            //                DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            //                if (!string.IsNullOrEmpty(PeriodTo))
            //                {
            //                    tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            //                }




            //                auditTrailList = auditTrailService.GetListAuditLog(Email, tPeriodFrom, tPeriodTo, ModuleName);
            //                List<ApplicationUser> listUsers = userService.GetUserByIdList(auditTrailList.Select(o => (long)o.UserID).ToArray());
            //                viewauditTrailList = (from a in auditTrailList
            //                                      select new AuditTrailViewModel.AuditlogViewModel
            //                                      {
            //                                          ColumnName = a.ColumnName,
            //                                          EventDate = a.EventDate,
            //                                          EventType = getAuditEventType(a.EventType.ToLower()),
            //                                          NewValue = a.NewValue
            //,
            //                                          OriginalValue = a.OriginalValue,
            //                                          RecordID = a.RecordID,
            //                                          TableName = a.TableName,
            //                                          UserEmail = GetUserEmailByID(listUsers, a.UserID)
            //                                      }).ToList();
            //            }

            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddDays(-auditLogsPeriod);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }




            auditTrailList = auditTrailService.GetListAuditLog(Email, tPeriodFrom, tPeriodTo, ModuleName);
            List<ApplicationUser> listUsers = userService.GetUserByIdList(auditTrailList.Select(o => (long)o.UserID).ToArray());
            viewauditTrailList = (from a in auditTrailList
                                  select new AuditTrailViewModel.AuditlogViewModel
                                  {
                                      ColumnName = a.ColumnName,
                                      EventDate = a.EventDate,
                                      EventType = getAuditEventType(a.EventType.ToLower()),
                                      NewValue = a.NewValue
,
                                      OriginalValue = a.OriginalValue,
                                      RecordID = a.RecordID,
                                      TableName = a.TableName,
                                      UserEmail = GetUserEmailByID(listUsers, a.UserID)
                                  }).ToList();
            IPagedList <AuditTrailViewModel.AuditlogViewModel> pagedItems = viewauditTrailList.ToPagedList<AuditTrailViewModel.AuditlogViewModel>(page - 1, paginationSize);
            ViewBag.pagedItems = pagedItems;
            ViewBag.varpage = page;
          

            AuditTrailViewModel.AuditlogSearchViewModel searchVM = new AuditTrailViewModel.AuditlogSearchViewModel();
            searchVM.Email = Email;
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            searchVM.moduleName = ModuleName;

            return View(AuditTrailView.AuditLogPage, searchVM);
        }
        private string getAuditEventType(string eventType)
        {
            if (eventType == "a")
                return "ADD";
            else if (eventType == "m")
                return "EDIT";
            else if (eventType == "d")
                return "DELETE";
            else if (eventType == "l")
                return "CHECK LOCK";

            return "";
        }

        // Post: Admin/MaintainableListPage
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("AuditlogPostSearch/{actionid}/{page}", Name = AuditTrailControllerRoute.AuditlogPostSearch)]
        public ActionResult AuditLogPostSearch(  int page, AuditTrailViewModel.AuditlogSearchViewModel searchVM)
        {
            return AuditLogPage(  1, searchVM.Email, searchVM.PeriodFrom, searchVM.PeriodTo, searchVM.moduleName);
        }


        public string GetUserEmailByID(int id)
        {
           
          return   userService.GetUserById(id)?.Email;
        }
        private string GetUserEmailByID(List<ApplicationUser> listUsers, int id)
        {
            ApplicationUser user = listUsers.Where(o => o.Id == id).SingleOrDefault();

            return user?.Email;
        }


        #endregion
    }
}