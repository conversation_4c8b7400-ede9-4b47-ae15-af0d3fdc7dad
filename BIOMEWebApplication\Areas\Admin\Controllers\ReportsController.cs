﻿using BIOME.Constants;
using BIOME.Services;
using BIOME.ViewModels;
using BIOME.Models;
using BIOMEWebApplication.Areas.Admin.Constants;
using MvcPaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using System.Globalization;
using BIOMEWebApplication.Authorization;

namespace BIOMEWebApplication.Areas.Admin.Controllers
{
#if INTERNET
    [AccessDeniedAuthorize(Roles = UserRoles.FullNoAccess)]
#elif INTRANET
    [AccessDeniedAuthorize(Roles = UserRoles.SystemAdmin)]
#endif
    [RoutePrefix("Reports")]

    public class ReportsController : AdminControllerBase
    {
        #region Fields

        private ISightingsService sightingsService;
        private IProjectsService projectsService;
        private IUserService userService;
        private IEmailService emailService;
        private IAuditTrailService auditTrailService;
        private IBadgesService badgesService;
        private IGroupService groupService;
        private ISystemParametersService systemParametersService;
        private IResourcesService resourceService;
        private IResearchService researchService;
        
        #endregion

        #region Constructors

        public ReportsController(ISightingsService sightingsService, IProjectsService projectsService, IEmailService emailService, IAuditTrailService auditTrailService, IBadgesService badgesService, IGroupService groupService, IPageService pageService, IUserService userService, ISystemParametersService systemParametersService, IResourcesService resourceService, IResearchService researchService) : base(pageService, userService)
        {
            this.sightingsService = sightingsService;
            this.projectsService = projectsService;
            this.userService = userService;
            this.emailService = emailService;
            this.auditTrailService = auditTrailService;
            this.badgesService = badgesService;
            this.groupService = groupService;
            this.systemParametersService = systemParametersService;
            this.resourceService = resourceService;
            this.researchService = researchService;
            ViewBag.MainMenu = BIOME.Enumerations.Menu.Admin.MainMenu.Reports;
        }

        protected override void Initialize(System.Web.Routing.RequestContext requestContext)
        {
            base.Initialize(requestContext);

            BreadcrumbsList.Add(new Breadcrumb() { Name = "Reports", UrlRootPath = Url.RouteUrl(ReportControllerRoute.GetSightingsPerUser) });
        }

        #endregion

        #region Download Csv

        // GET: Admin/Reports
        [Route("DownloadCsv/{type}", Name = ReportControllerRoute.GetDownloadCsv)]
        public ActionResult DownloadCsv(string type)
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            var DocName = "";
            switch (type)
            {
                case "SightingsPerUser":
                    DocName = "Number of sightings per user";
                    break;
                case "SightingVotesPerUser":
                    DocName = "Number of votes per user";
                    break;
                case "AccurateVoter":
                    DocName = "Most accurate voters";
                    break;
                case "PopularCategories":
                    DocName = "Popular Categories";
                    break;
                case "PopularSpecies":
                    DocName = "Popular Species";
                    break;
                case "ProjectSighting":
                    DocName = "Number of sightings in projects";
                    break;
                case "ProjectMember":
                    DocName = "Number of members in projects";
                    break;
                case "ProjectPopularCategories":
                    DocName = "Most popular Categories";
                    break;
                case "BadgeUserNumber":
                    DocName = "No. of users by badges";
                    break;
                default:
                    DocName = "";
                    break;
            }

            auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.Download_Reports, "ReportName: " + DocName);
            if (string.Equals(type, "SightingsPerUser"))
            {
                List<ReportViewModel.UserIdNumber> listSightingsNumberPerUser = sightingsService.GetSightingsNumberPerUserList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Email,Number");
                csv.AppendLine();
                foreach (var item in listSightingsNumberPerUser)
                {
                    item.applicationUser = userService.GetUserById(item.userId);
                    string rowstring = item.applicationUser.Email + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "SightingsPerUser_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "SightingVotesPerUser"))
            {
                List<ReportViewModel.UserIdNumber> listSightingsNumberPerUser = sightingsService.GetVoteNumberPerUserList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Email,Number");
                csv.AppendLine();
                foreach (var item in listSightingsNumberPerUser)
                {
                    item.applicationUser = userService.GetUserById(item.userId);
                    string rowstring = item.applicationUser.Email + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "VotesPerUser_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "AccurateVoter"))
            {
                List<ReportViewModel.UserIdNumber> listAccurateVoter = sightingsService.GetAccurateVoterList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Email,Number");
                csv.AppendLine();
                foreach (var item in listAccurateVoter)
                {
                    item.applicationUser = userService.GetUserById(item.userId);
                    string rowstring = item.applicationUser.Email + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "AccurateVoter_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "PopularCategories"))
            {
                List<ReportViewModel.GroupNameNumber> listPopularCategories = sightingsService.GetPopularCategoriesList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Name,Number");
                csv.AppendLine();
                foreach (var item in listPopularCategories)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.groupName) + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "PopularCategories_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "PopularSpecies"))
            {
                List<ReportViewModel.GroupNameNumber> listPopularSpecies = sightingsService.GetPopularSpeciesList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Name,Number");
                csv.AppendLine();
                foreach (var item in listPopularSpecies)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.groupName) + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "PopularSpecies_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "ProjectSighting"))
            {
                List<ProjectDetail> listProjectDetail = projectsService.GetCMSAllProjectList().OrderByDescending(m => m.SightingCount).ToList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Title,Number");
                csv.AppendLine();
                foreach (var item in listProjectDetail)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.Title) + "," + item.SightingCount;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "ProjectSighting_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "ProjectMember"))
            {
                List<ProjectDetail> listProjectDetail = projectsService.GetCMSAllProjectList().OrderByDescending(m => m.MemberCount).ToList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Title,Number");
                csv.AppendLine();
                foreach (var item in listProjectDetail)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.Title) + "," + item.MemberCount;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "ProjectMember_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "ProjectPopularCategories"))
            {
                List<ReportViewModel.GroupNameNumber> listPopularCategories = sightingsService.GetProjectPopularCategoriesList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Name,Number");
                csv.AppendLine();
                foreach (var item in listPopularCategories)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.groupName) + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "ProjectPopularCategories_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }
            else if (string.Equals(type, "BadgeUserNumber"))
            {
                List<ReportViewModel.GroupNameNumber> listBadgeUserNumber = badgesService.GetBadgeUserNumberList();
                System.Text.StringBuilder csv = new System.Text.StringBuilder();
                csv.Append("Title,Number");
                csv.AppendLine();
                foreach (var item in listBadgeUserNumber)
                {
                    string rowstring = Utilities.Helpers.CsvHelper.Escape(item.groupName) + "," + item.number;
                    csv.Append(rowstring);
                    csv.AppendLine();
                }
                return File(new System.Text.UTF8Encoding().GetBytes(csv.ToString()), "text/csv", "ProjectPopularCategories_" + DateTimeOffset.Now.ToString("ddMMyyyy", new System.Globalization.CultureInfo("en-SG")) + ".csv");
            }

            return new EmptyResult();
        }

        #endregion

        #region User Report

        // GET: Admin/Reports
        [Route("NewUsers", Name = ReportControllerRoute.GetNewUsers)]
        public ActionResult NewUsers(string PeriodFrom = "", string PeriodTo = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Users;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Users", UrlRootPath = "#" });

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_UsersReport, "");

            string showTimePeriod = "All Time Period";
            //DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddMonths(-1);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                
            }
            else
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }

            showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;

            int totalNumber = auditTrailService.GetNewUsersNumber(-1, tPeriodFrom, tPeriodTo);
            int mobileNumber = auditTrailService.GetNewUsersNumber(0, tPeriodFrom, tPeriodTo);
            int webNumber = auditTrailService.GetNewUsersNumber(1, tPeriodFrom, tPeriodTo);

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            ViewBag.showTimePeriod = showTimePeriod;
            ViewBag.totalNumber = totalNumber;
            ViewBag.mobileNumber = mobileNumber;
            ViewBag.webNumber = webNumber;

            return View(ReportView.NewUsers, searchVM);
        }

        // GET: Admin/Reports
        [Route("ActiveUsers", Name = ReportControllerRoute.GetActiveUsers)]
        public ActionResult ActiveUsers(string PeriodFrom = "", string PeriodTo = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Users;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Users", UrlRootPath = "#" });

            string showTimePeriod = "All Time Period";
            //DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddMonths(-1);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                
            }
            else
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }

            showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;

            int allUserNumber = auditTrailService.GetAllUsersNumber();
            int totalNumber = sightingsService.GetActiveUsersNumber(-1, tPeriodFrom, tPeriodTo);
            int bothNumber = sightingsService.GetActiveUsersNumber(-2, tPeriodFrom, tPeriodTo);
            int mobileNumber = sightingsService.GetActiveUsersNumber((int)BIOME.Enumerations.Sighting.SubmitFromList.Mobile, tPeriodFrom, tPeriodTo);
            int webNumber = sightingsService.GetActiveUsersNumber((int)BIOME.Enumerations.Sighting.SubmitFromList.Website, tPeriodFrom, tPeriodTo);

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            ViewBag.showTimePeriod = showTimePeriod;
            ViewBag.totalNumber = totalNumber;
            ViewBag.mobileNumber = mobileNumber;
            ViewBag.webNumber = webNumber;
            ViewBag.allUserNumber = allUserNumber;
            ViewBag.bothNumber = bothNumber;

            return View(ReportView.ActiveUsers, searchVM);
        }

        // GET: Admin/Reports
        [Route("LoginNumber", Name = ReportControllerRoute.GetLoginNumber)]
        public ActionResult LoginNumber(string PeriodFrom = "", string PeriodTo = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Users;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Users", UrlRootPath = "#" });

            string showTimePeriod = "All Time Period";
            //DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddMonths(-1);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }
            else 
            {
                PeriodFrom = tPeriodFrom.ToString("dd/MM/yyyy HH:mm");
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                
            }
            else 
            {
                PeriodTo = tPeriodTo.ToString("dd/MM/yyyy HH:mm");
            }

            showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;

            int weblogin = auditTrailService.GetLoginNumber("Website Login Successfully", tPeriodFrom, tPeriodTo);
            //int webfblogin = 0; //Web facebook login is "Website Login Successfully (Facebook)". already included in weblogin
            int apilogin = auditTrailService.GetLoginNumber("API Login Successful", tPeriodFrom, tPeriodTo);
            int apifblogin = auditTrailService.GetLoginNumber("APIFacebook Login Successful", tPeriodFrom, tPeriodTo);
            int totalNumber = weblogin + apilogin+ apifblogin;
            int mobileNumber = apilogin + apifblogin;
            int webNumber = weblogin;

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            ViewBag.showTimePeriod = showTimePeriod;
            ViewBag.totalNumber = totalNumber;
            ViewBag.mobileNumber = mobileNumber;
            ViewBag.webNumber = webNumber;

            return View(ReportView.LoginNumber, searchVM);
        }

        // GET: Admin/Reports
        [Route("GroupUserNumber", Name = ReportControllerRoute.GetGroupUserNumber)]
        public ActionResult GroupUserNumber()
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Users;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Users", UrlRootPath = "#" });

            var groupUserNumber = new Dictionary<long, int>();
            var Groups = groupService.GetGroupsHierarchy().ToList();
            foreach (var item in Groups)
            {
                int userNumber = auditTrailService.GetGroupUserNumber(item.Id);
                groupUserNumber.Add(item.Id, userNumber);
            }

            ViewBag.groupUserNumber = groupUserNumber;
            ViewBag.Groups = Groups;
            ViewBag.Title = "Reports";

            return View(ReportView.GroupUserNumber);
        }

        // GET: Admin/Reports
        [Route("BadgeUserNumber", Name = ReportControllerRoute.GetBadgeUserNumber)]
        public ActionResult BadgeUserNumber()
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Users;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Users", UrlRootPath = "#" });

            List<ReportViewModel.GroupNameNumber> listBadgeUserNumber = badgesService.GetBadgeUserNumberList();
            ViewBag.Title = "Reports";
            ViewBag.listBadgeUserNumber = listBadgeUserNumber;

            return View(ReportView.BadgeUserNumber);
        }

        #endregion

        #region Sighting Report

        // GET: Admin/Reports
        [Route("SightingsNumber", Name = ReportControllerRoute.GetSightingsNumber)]
        public ActionResult SightingsNumber(string PeriodFrom = "", string PeriodTo = "")
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Sightings;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Sightings", UrlRootPath = "#" });

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_SightingsReport, "");

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            int totalNumber = sightingsService.GetSightingsNumberDeviceTime(-1, tPeriodFrom, tPeriodTo);
            int mobileNumber = sightingsService.GetSightingsNumberDeviceTime((int)BIOME.Enumerations.Sighting.SubmitFromList.Mobile, tPeriodFrom, tPeriodTo);
            int webNumber = sightingsService.GetSightingsNumberDeviceTime((int)BIOME.Enumerations.Sighting.SubmitFromList.Website, tPeriodFrom, tPeriodTo);

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            ViewBag.showTimePeriod = showTimePeriod;
            ViewBag.totalNumber = totalNumber;
            ViewBag.mobileNumber = mobileNumber;
            ViewBag.webNumber = webNumber;

            return View(ReportView.SightingsNumber, searchVM);
        }

        // Post: Admin/Reports
        [Authorize]
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Route("PostSearch/{type}", Name = ReportControllerRoute.PostSearch)]
        public ActionResult PostSearch(string type, ReportViewModel.SearchViewModel searchVM)
        {
            if (string.Equals(type, "SightingsNumber"))
            {
                return SightingsNumber(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "NewUsers"))
            {
                return NewUsers(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "ActiveUsers"))
            {
                return ActiveUsers(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "LoginNumber"))
            {
                return LoginNumber(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "NumberOfUploads"))
            {
                return NumberOfUploads(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "ResearchPermitApplication"))
            {
                return GetResearchPermitApplication(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            else if (string.Equals(type, "ReportsSubmittedByPermit"))
            {
                return GetReportsSubmittedByPermits(searchVM.PeriodFrom, searchVM.PeriodTo);
            }
            return null;
        }

        // GET: Admin/Reports
        [Route("SightingsPerUser/{page}", Name = ReportControllerRoute.GetSightingsPerUser)]
        public ActionResult SightingsPerUser(int page = 1)
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Sightings;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Sightings", UrlRootPath = "#" });
            List<ReportViewModel.UserIdNumber> listSightingsNumberPerUser = sightingsService.GetSightingsNumberPerUserList();
            ViewBag.Title = "Reports";
            int paginationSize = systemParametersService.GetPaginationPageSize();
            IPagedList<ReportViewModel.UserIdNumber> pagedUserIdNumberList = listSightingsNumberPerUser.ToPagedList<ReportViewModel.UserIdNumber>(page - 1, paginationSize);
            ViewBag.pagedUserIdNumberList = pagedUserIdNumberList;
            foreach (var item in pagedUserIdNumberList)
            {
                item.applicationUser = userService.GetUserById(item.userId);
            }

            ViewBag.varpage = page;

            return View(ReportView.SightingsPerUser);
        }

        // GET: Admin/Reports
        [Route("SightingVotesPerUser/{page}", Name = ReportControllerRoute.GetSightingVotesPerUser)]
        public ActionResult SightingVotesPerUser(int page = 1)
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Sightings;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Sightings", UrlRootPath = "#" });
            List<ReportViewModel.UserIdNumber> listSightingsNumberPerUser = sightingsService.GetVoteNumberPerUserList();
            ViewBag.Title = "Reports";
            int paginationSize = systemParametersService.GetPaginationPageSize();
            IPagedList<ReportViewModel.UserIdNumber> pagedUserIdNumberList = listSightingsNumberPerUser.ToPagedList<ReportViewModel.UserIdNumber>(page - 1, paginationSize);
            ViewBag.pagedUserIdNumberList = pagedUserIdNumberList;
            foreach (var item in pagedUserIdNumberList)
            {
                var applicationUser = userService.GetUserById(item.userId);
                if (applicationUser != null)
                    item.applicationUser = applicationUser;
                else
                {
                    item.applicationUser = new ApplicationUser();
                    item.applicationUser.Email = "-";
                }
            }

            ViewBag.varpage = page;

            return View(ReportView.SightingVotesPerUser);
        }

        // GET: Admin/Reports
        [Route("SightingTop10", Name = ReportControllerRoute.GetSightingTop10)]
        public ActionResult SightingTop10()
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Sightings;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Sightings", UrlRootPath = "#" });
            List<ReportViewModel.UserIdNumber> listAccurateVoter = sightingsService.GetAccurateVoterList();
            List<ReportViewModel.GroupNameNumber> listPopularCategories = sightingsService.GetPopularCategoriesList();
            List<ReportViewModel.GroupNameNumber> listPopularSpecies = sightingsService.GetPopularSpeciesList();
            ViewBag.Title = "Reports";
            ViewBag.listAccurateVoter = listAccurateVoter;
            ViewBag.listPopularCategories = listPopularCategories;
            ViewBag.listPopularSpecies = listPopularSpecies;
            foreach (var item in listAccurateVoter)
            {
                var applicationUser = userService.GetUserById(item.userId);
                if (applicationUser != null)
                    item.applicationUser = applicationUser;
                else
                {
                    item.applicationUser = new ApplicationUser();
                    item.applicationUser.Email = "-";
                }
            }

            return View(ReportView.SightingTop10);
        }

        #endregion

        #region Project Report

        // GET: Admin/Reports
        [Route("ProjectMember/{page}", Name = ReportControllerRoute.GetProjectMember)]
        public ActionResult ProjectMember(int page = 1)
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Projects;

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_ProjectReport, "");

            ConstructBreadcrumbs(new Breadcrumb() { Name = "Projects", UrlRootPath = "#" });
            List<ProjectDetail> listProjectDetail = projectsService.GetCMSAllProjectList().OrderByDescending(m => m.MemberCount).ToList();
            ViewBag.Title = "Reports";
            int paginationSize = systemParametersService.GetPaginationPageSize();
            IPagedList<ProjectDetail> pagedProjectDetail = listProjectDetail.ToPagedList<ProjectDetail>(page - 1, paginationSize);
            ViewBag.pagedProjectDetail = pagedProjectDetail;

            ViewBag.varpage = page;

            return View(ReportView.ProjectMember);
        }

        // GET: Admin/Reports
        [Route("ProjectSighting/{page}", Name = ReportControllerRoute.GetProjectSighting)]
        public ActionResult ProjectSighting(int page = 1)
        {
#if INTRANET
            if (Session["OTP_VERIFIED"] == null || Session["OTP_VERIFIED"] == "")
                return RedirectToRoute(HomeControllerRoute.GetEnterOTP, new { returnUrl = Request.Url.PathAndQuery });
#endif
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Projects;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Projects", UrlRootPath = "#" });
            List<ProjectDetail> listProjectDetail = projectsService.GetCMSAllProjectList().OrderByDescending(m => m.SightingCount).ToList();
            ViewBag.Title = "Reports";
            int paginationSize = systemParametersService.GetPaginationPageSize();
            IPagedList<ProjectDetail> pagedProjectDetail = listProjectDetail.ToPagedList<ProjectDetail>(page - 1, paginationSize);
            ViewBag.pagedProjectDetail = pagedProjectDetail;

            ViewBag.varpage = page;

            return View(ReportView.ProjectSighting);
        }

        // GET: Admin/Reports
        [Route("ProjectPopularCategories", Name = ReportControllerRoute.GetProjectPopularCategories)]
        public ActionResult ProjectPopularCategories()
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Projects;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Projects", UrlRootPath = "#" });
            List<ReportViewModel.GroupNameNumber> listPopularCategories = sightingsService.GetProjectPopularCategoriesList();
            ViewBag.Title = "Reports";
            ViewBag.listPopularCategories = listPopularCategories;

            return View(ReportView.ProjectPopularCategories);
        }

        #endregion

        #region Resource Report

        //Number of uploads by user
        // GET: Admin/Reports
        [Route("NumberOfUploads/{page}", Name = ReportControllerRoute.GetNumberOfUploads)]
        public ActionResult NumberOfUploads(string PeriodFrom = "", string PeriodTo = "", int page = 1)
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Resource;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Resource", UrlRootPath = "#" });

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_ResourceReport, "");

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }
            
            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            IList<ReportViewModel.GroupNameNumber> listNumberOfUploads = resourceService.GetNumberOfUploadsByUser(tPeriodFrom, tPeriodTo);
            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listNumberOfUploads.Count())
                page = 1;
            IPagedList<ReportViewModel.GroupNameNumber> listNumberOfUploadsCount = listNumberOfUploads.ToPagedList<ReportViewModel.GroupNameNumber>(page - 1, paginationSize);

            ViewBag.listNumberOfUploads = listNumberOfUploadsCount;
            ViewBag.varpage = page;
            return View(ReportView.NumberOfUploads, searchVM);
        }

        //Most popular Locations
        // GET: Admin/Reports
        [Route("PopularLocations", Name = ReportControllerRoute.GetPopularLocations)]
        public ActionResult PopularLocations(string PeriodFrom = "", string PeriodTo = "")
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Resource;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Resource", UrlRootPath = "#" });

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            ViewBag.listNumberOfUploads = resourceService.GetMostPopularCategories();
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;
           
            return View(ReportView.PopularLocations, searchVM);
        }

        //Most popular Categories
        // GET: Admin/Reports
        [Route("PopularCategories", Name = ReportControllerRoute.GetPopularCategories)]
        public ActionResult PopularCategories(string PeriodFrom = "", string PeriodTo = "")
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.Resource;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Resource", UrlRootPath = "#" });

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            ViewBag.listCategoriesCount = resourceService.GetMostPopularCategories();
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;


            return View(ReportView.PopularCategories, searchVM);
        }

        #endregion

        #region Research Permit

        // GET: Admin/Reports
        //Research Permit Application
        [Route("ResearchPermitApplication", Name = ReportControllerRoute.GetResearchPermitApplication)]
        public ActionResult GetResearchPermitApplication(string PeriodFrom = "", string PeriodTo = "")
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.ResearchPermitApplication;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Research Permit", UrlRootPath = "#" });

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_PermitReport, "");

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            ViewBag.totalApplications = researchService.GetResearchPermitApplication(tPeriodFrom, tPeriodTo);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            return View(ReportView.ResearchPermitApplication, searchVM);
        }

        [Route("ReportsSubmittedByPermit/{page}", Name = ReportControllerRoute.GetReportsSubmittedByPermits)]
        public ActionResult GetReportsSubmittedByPermits(string PeriodFrom = "", string PeriodTo = "", int page=1)
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.ResearchPermitApplication;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Research Permit", UrlRootPath = "#" });

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            IList<ReportViewModel.GroupNameNumber> listResearchReports = researchService.GetReportsSubmittedByPermit(tPeriodFrom, tPeriodTo);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listResearchReports.Count())
                page = 1;
            IPagedList<ReportViewModel.GroupNameNumber> listResearchReportsCount = listResearchReports.ToPagedList<ReportViewModel.GroupNameNumber>(page - 1, paginationSize);
            ViewBag.listResearchReportsCount = listResearchReportsCount;

            ViewBag.varpage = page;


            return View(ReportView.ResearchReportsSubmitted, searchVM);
        }

        //CR3&4 Phase2.
        [Route("GetCoralCollectionSummary/{page}", Name = ReportControllerRoute.GetCoralCollectionSummary)]
        public ActionResult GetCoralCollectionSummary(string PeriodFrom = "", string PeriodTo = "", int page = 1)
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.CoralCollection;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Coral Collection Summary", UrlRootPath = "#" });

            if (!IsPostBack() && Request.HttpMethod == "GET")
                auditTrailService.LogAuditTrail(GetUserId(), BIOME.Enumerations.Audit.Module.Reports, BIOME.Enumerations.Audit.AuditLogAction.View_CoralCollectionDataReport, "");

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = new DateTimeOffset(2021, 03, 01, 0, 0, 0, TimeSpan.Zero);  //CoralCollection added on 2021
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewModel searchVM = new ReportViewModel.SearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;

            IList<ReportViewModel.CoralCollectionSummary> listResearchReports = researchService.GetCoralCollectionSummary(tPeriodFrom, tPeriodTo);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listResearchReports.Count())
                page = 1;
            IPagedList<ReportViewModel.CoralCollectionSummary> listReport = listResearchReports.ToPagedList<ReportViewModel.CoralCollectionSummary>(page - 1, paginationSize);
            ViewBag.listReport = listReport;

            ViewBag.varpage = page;


            return View(ReportView.CoralCollectionSummary, searchVM);
        }

        //CR3&4 Phase2
        [Route("CoralCollectionDetails/{page}", Name = ReportControllerRoute.GetCoralCollectionDetails)]
        public ActionResult GetCoralCollectionDetails(string PeriodFrom = "", string PeriodTo = "", int page = 1, int filterYear=0)
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.CoralCollection;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Coral Collection Summary", 
                UrlRootPath = Url.RouteUrl(ReportControllerRoute.GetCoralCollectionSummary, new { page = 1, PeriodFrom = PeriodFrom, PeriodTo = PeriodTo})
            }, new Breadcrumb() { Name = "Coral Collection Details", UrlRootPath = "#" });

            if (filterYear == 0)
                filterYear = DateTime.Now.Year;

            string showTimePeriod = "All Time Period";
            //DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            DateTimeOffset tPeriodFrom = new DateTimeOffset(2021, 03, 01, 0, 0, 0, TimeSpan.Zero);  //CoralCollection added on 2021
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.CoralCollectionSearchViewModel searchVM = new ReportViewModel.CoralCollectionSearchViewModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            searchVM.FilterYear = filterYear;

            IList<ReportViewModel.CoralCollectionDetails> listResearchReports = researchService.GetCoralCollectionDetails(tPeriodFrom, tPeriodTo,filterYear);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listResearchReports.Count())
                page = 1;
            IPagedList<ReportViewModel.CoralCollectionDetails> listReport = listResearchReports.ToPagedList<ReportViewModel.CoralCollectionDetails>(page - 1, paginationSize);
            ViewBag.listReport = listReport;

            ViewBag.varpage = page;


            return View(ReportView.CoralCollectionDetails, searchVM);
        }

        [Route("NumberPermitsByOrganisation", Name = ReportControllerRoute.GetNumberPermitsByOrganisation)]
        public ActionResult GetNumberPermitsByOrganisation(string PeriodFrom = "", string PeriodTo = "")
        {
            ViewBag.ReportMenu = BIOME.Enumerations.Menu.Admin.ReportSubMenu.ResearchPermitApplication;
            ConstructBreadcrumbs(new Breadcrumb() { Name = "Research Permit", UrlRootPath = "#" });

            var groupPermitsNumber = new Dictionary<long, int>();
            var Groups = groupService.GetGroupsHierarchy().ToList();
            
            foreach (var item in Groups)
            {
                //int userNumber = auditTrailService.GetGroupUserNumber(item.Id);
                int permitsNumber = researchService.GetGroupPermitApplicationCount(item.Id);
                groupPermitsNumber.Add(item.Id, permitsNumber);
            }

            ViewBag.groupPermitsNumber = groupPermitsNumber;
            ViewBag.Groups = Groups;
            ViewBag.Title = "Reports";

            return View(ReportView.ResearchNumberPermitsByOrg);
        }

        [Route("ResponseTimeToApplication/{page}", Name = ReportControllerRoute.GetResponseTimeApplication)]
        public ActionResult ResponseTimeToApplication(string PeriodFrom = "", string PeriodTo = "", string permitNumber = "", int numberOfDays=4, int page = 1)
        {

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }
            
            ReportViewModel.SearchViewISOModel searchVM = new ReportViewModel.SearchViewISOModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            searchVM.PermitNumber = permitNumber;
            searchVM.NumberOfDays = numberOfDays;

            IList<ReportViewModel.PermitApplicationValues> listResponseTimeApplications = researchService.GetResponseTimeToApplication(tPeriodFrom, tPeriodTo, permitNumber, numberOfDays);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listResponseTimeApplications.Count())
                page = 1;
            IPagedList<ReportViewModel.PermitApplicationValues> listResponseTimeApplication = listResponseTimeApplications.ToPagedList<ReportViewModel.PermitApplicationValues>(page - 1, paginationSize);
            ViewBag.listResponseTimeApplication = listResponseTimeApplication;

            ViewBag.varpage = page;


            return View(ReportView.ResponseTimeToApplication, searchVM);
        }

        [Route("ResponseTimeToIssuePermit/{page}", Name = ReportControllerRoute.GetResponseTimeToIssuePermit)]
        public ActionResult ResponseTimeToIssuePermit(string PeriodFrom = "", string PeriodTo = "", string permitNumber = "", int numberOfDays = 4, int page = 1)
        {

            string showTimePeriod = "All Time Period";
            DateTimeOffset tPeriodFrom = DateTimeOffset.Now.AddYears(-10);
            if (!string.IsNullOrEmpty(PeriodFrom))
            {
                tPeriodFrom = DateTimeOffset.ParseExact(PeriodFrom, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
            }

            DateTimeOffset tPeriodTo = DateTimeOffset.Now;
            if (!string.IsNullOrEmpty(PeriodTo))
            {
                tPeriodTo = DateTimeOffset.ParseExact(PeriodTo, "dd/MM/yyyy HH:mm", new CultureInfo("en-US"));
                showTimePeriod = "From " + PeriodFrom + " To " + PeriodTo;
            }

            ReportViewModel.SearchViewISOModel searchVM = new ReportViewModel.SearchViewISOModel();
            searchVM.PeriodFrom = PeriodFrom;
            searchVM.PeriodTo = PeriodTo;
            searchVM.PermitNumber = permitNumber;
            searchVM.NumberOfDays = numberOfDays;

            IList<ReportViewModel.PermitApplicationValues> listResponseTimeApplications = researchService.GetResponseTimeToIssuePermit(tPeriodFrom, tPeriodTo, permitNumber, numberOfDays);
            ViewBag.Title = "Reports";
            ViewBag.showTimePeriod = showTimePeriod;

            int paginationSize = systemParametersService.GetPaginationPageSize();
            if (paginationSize > listResponseTimeApplications.Count())
                page = 1;
            IPagedList<ReportViewModel.PermitApplicationValues> listResponseTimeApplication = listResponseTimeApplications.ToPagedList<ReportViewModel.PermitApplicationValues>(page - 1, paginationSize);
            ViewBag.listResponseTimeApplication = listResponseTimeApplication;

            ViewBag.varpage = page;


            return View(ReportView.ResponseTimeToIssuePermit, searchVM);
        }
        #endregion
    }
}