﻿using AutoMapper;
using BIOME.Models;
using BIOME.ViewModels;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Utilities.Helpers;
using BIOME.Constants;
using Facebook;
using System.Security.Claims;
using BIOME.ViewModels.API.v1_0;
using MvcPaging;
using Newtonsoft.Json;
using System.Web;
using System.Net;
using System.Configuration;
using System.IO;
using CsvHelper;
using BIOME.ViewModels.CSVMapper;
using log4net;
using static BIOME.Constants.Configuration.Server;
using CsQuery.ExtensionMethods;
//using RestSharp.Extensions;
//using Nest;
using System.Net.Http;
using static BIOME.ViewModels.UserViewModel;
using CsQuery.ExtensionMethods.Internal;
using static BIOME.ViewModels.UserGroupViewModel;
using static BIOME.Enumerations.User;
using System.Web.Security;
using System.Data.Entity;
using CsvHelper.Configuration;
using dbAutoTrack.PDFWriter;
using static BIOME.Constants.Configuration;
using System.Globalization;
//using RazorEngine.Compilation.ImpromptuInterface.Optimization;

namespace BIOME.Services
{
    public class UserServiceNoOWIN : IUserServiceNoOWIN, IUserQueryService
    {
        #region Fields

        private readonly ApplicationUserManager userManager;
        private readonly ApplicationRoleManager roleManager;
        private readonly IGroupService groupService;
        private readonly ISystemParametersService systemParameterService;
        //private readonly IBadgesService badgesService;
        private readonly IBackgroundJobService backgroundJobService;
        //private readonly IEmailService emailService;
        //private readonly IAuditTrailService auditTrailService;
        private readonly ApplicationDbContext dbContext;
        private static readonly log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


        #endregion

        #region Constructors

        public UserServiceNoOWIN(
            ApplicationUserManager userManager,
            ApplicationRoleManager roleManager,
            IGroupService groupService,
            ISystemParametersService systemParameterService,
            //IBadgesService badgesService,
            IBackgroundJobService backgroundJobService,
            //IEmailService emailService,
           // IAuditTrailService auditTrailService,
            ApplicationDbContext dbContext)
        {
            this.userManager = userManager;
            this.roleManager = roleManager;
            this.groupService = groupService;
            this.systemParameterService = systemParameterService;
            //this.badgesService = badgesService;
            //this.emailService = emailService;
            //this.auditTrailService = auditTrailService;
            this.backgroundJobService = backgroundJobService;
            this.dbContext = dbContext;
        }

        #endregion

        #region Public Methods
        static byte[] GetBytes(string str)
        {
            byte[] bytes = new byte[str.Length * sizeof(char)];
            System.Buffer.BlockCopy(str.ToCharArray(), 0, bytes, 0, bytes.Length);
            return bytes;
        }

        /*  public bool AddToFileStorageTmp(string fileName, byte[] fileData, long objectdID, bool hasDownloadedToInternetApp, bool hasDownloadedToIntranetApp, string title = "")
          {
              //under UserService class
              return false;
          }

          public bool AddToFileStorageTmp(List<FileStorageTmpModel> fileStorageTmpModel, bool hasDownloadedToInternetApp, bool hasDownloadedToIntranetApp) { 
              //it is implemented under UserService class
              return false;   

          }*/

        #region fileUpload
        public bool AddToFileStorageTmp(string fileName, byte[] fileData, long objectdID, bool hasDownloadedToInternetApp, bool hasDownloadedToIntranetApp, string title = "")
        {
            FileStoreTmp fileStoreTmp = new FileStoreTmp();

            if (!title.IsNullOrEmpty())
                fileStoreTmp.FileName = fileName + "|" + title;
            else
                fileStoreTmp.FileName = fileName;

            fileStoreTmp.Data = fileData;
            fileStoreTmp.ObjectID = objectdID;
            fileStoreTmp.hasDownloadedToInternetApp = hasDownloadedToInternetApp;
            fileStoreTmp.hasDownloadedToIntranetApp = hasDownloadedToIntranetApp;
            dbContext.FileStoreTmps.Add(fileStoreTmp);

            int rtnVal = dbContext.SaveChangesOnly();

            if (rtnVal > 0)
            {
                if (hasDownloadedToInternetApp)
                {
                    backgroundJobService.ScheduleSyncFilesForApp2ByFile(new BackgroundJobService.BackgroundJobSceduleDelayed()
                    {
                        Delay = TimeSpan.FromSeconds(3)
                    }, fileStoreTmp.Id);
                }
                else if (hasDownloadedToIntranetApp)
                {
                    backgroundJobService.ScheduleSyncFilesForApp1ByFile(new BackgroundJobService.BackgroundJobSceduleDelayed()
                    {
                        Delay = TimeSpan.FromSeconds(3)
                    }, fileStoreTmp.Id);
                }

            }

            return (rtnVal > 0);

        }

        public bool AddToFileStorageTmp(List<FileStorageTmpModel> fileStorageTmpModel, bool hasDownloadedToInternetApp, bool hasDownloadedToIntranetApp)
        {

            FileStoreTmp fileStoreTmp = null;
            List<FileStoreTmp> filestoreList = new List<FileStoreTmp>();
            foreach (FileStorageTmpModel filestore in fileStorageTmpModel)
            {
                fileStoreTmp = new FileStoreTmp();

                fileStoreTmp.FileName = filestore.fileName;
                fileStoreTmp.Data = filestore.fileData;
                fileStoreTmp.ObjectID = filestore.objectdID;
                fileStoreTmp.hasDownloadedToInternetApp = hasDownloadedToInternetApp;
                fileStoreTmp.hasDownloadedToIntranetApp = hasDownloadedToIntranetApp;

                filestoreList.Add(fileStoreTmp);

            }

            dbContext.FileStoreTmps.AddRange(filestoreList);
            int rtnVal = dbContext.SaveChangesOnly();

            if (rtnVal > 0)
            {
                var storeIDs = filestoreList.Select(t => t.Id).ToArray();
                if (hasDownloadedToInternetApp)
                {
                    backgroundJobService.ScheduleSyncFilesForApp2ByFile(new BackgroundJobService.BackgroundJobSceduleDelayed()
                    {
                        Delay = TimeSpan.FromSeconds(10)
                    }, storeIDs);
                }
                else if (hasDownloadedToIntranetApp)
                {
                    backgroundJobService.ScheduleSyncFilesForApp1ByFile(new BackgroundJobService.BackgroundJobSceduleDelayed()
                    {
                        Delay = TimeSpan.FromSeconds(10)
                    }, storeIDs);
                }

            }

            return (rtnVal > 0);

        }
        #endregion

        //old format
        /*public byte[] ExportNparksUsersInfoForAudit()
        {
            var activeUsers = GetUsers().ToList();

            var list = new List<UserViewModel.NparksUserExportCSVViewModel>();
            foreach (var activeUser in activeUsers)
            {
                //var roles = userManager.GetRoles(activeUser.Id);

                var exportItem = new UserViewModel.NparksUserExportCSVViewModel();
                var logins = userManager.GetLogins(activeUser.Id);
                var groupname = "";
                if (activeUser.Group != null)
                    groupname = activeUser.Group.Name.ToLower();
                if (logins.Count > 0)
                {
                    if (groupname != BIOME.Constants.Account.UserGroup.Public.ToLower())
                    {

                        exportItem.SOEID = "N/A";

                        exportItem.STAFFNAME = activeUser.PersonName;
                        exportItem.ACCSTATUS = (activeUser.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE : ((activeUser.AccountStatus == BIOME.Constants.Account.AccountStatus.DEACTIVATED ? activeUser.AccountStatus : BIOME.Constants.Account.AccountStatus.SUSPENDED)));
                        exportItem.EMAIL = activeUser.Email;
                        exportItem.LASTDEACTIVATEDATE = activeUser.LastDeActivateDate.Year > 2000 ? activeUser.LastDeActivateDate.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                        exportItem.LASTSUSPENDDATE = activeUser.LastSuspendDate.Year > 2000 ? activeUser.LastSuspendDate.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                        exportItem.LASTLOGINDATE = activeUser.DateLastLogin.Year > 2000 ? activeUser.DateLastLogin.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                        exportItem.ENDDATE = "";
                        exportItem.ROLECODE = exportItem.ROLEDESC = String.Join(" ", GetRolesForUser(activeUser.Id).ToList().ToArray());// GetRolesForUser(activeUser.Id).ToList();
                        exportItem.STARTDATE = "";
                        exportItem.USERID = logins.FirstOrDefault().ProviderKey;

                        list.Add(exportItem);
                    }
                }
            }

            StringWriter sw = new StringWriter();
            var csv = new CsvWriter(sw);
            csv.Configuration.RegisterClassMap<NparksUserExportCSVMapper>();
            csv.WriteHeader<UserViewModel.NparksUserExportCSVViewModel>();
            csv.WriteRecords(list);
            return GetBytes(sw.ToString());
        }*/
        public string ExportNparksUsersInfoForAudit()
        {
            //var export_users = GetUsers().ToList();
            var export_users = GetAllUsers();

            Group nparkGroup = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.NParks);
            var allNparkGrups = groupService.GetAllChildrenGroups(nparkGroup);
            var allNparkGrupNames = allNparkGrups.Select(t => t.Name).ToArray().Select(t => t.ToLower());
            var list = new List<UserViewModel.NparksUserExport_ACE_CSVViewModel>();

            foreach (var export_user in export_users)
            {
                //var roles = userManager.GetRoles(activeUser.Id);

                var exportItem = new UserViewModel.NparksUserExport_ACE_CSVViewModel();
                //var logins = userManager.GetLogins(export_user.Id);
                var groupname = "";
                if (export_user.Group != null)
                {
                    groupname = export_user.Group.Name.ToLower();

                }
                //if (groupname == BIOME.Constants.Account.UserGroup.NParks.ToLower())
                if (allNparkGrupNames.Contains(groupname))
                {
                    exportItem.USERID = export_user.UserName.Replace("|", "").Trim();
                    if (export_user.Logins.Count() > 0)
                    {
                        //exportItem.SOEID = logins.FirstOrDefault().ProviderKey;
                        exportItem.SOEID = export_user.Logins.FirstOrDefault().ProviderKey.Trim();
                    }
                    exportItem.STAFFNAME = string.IsNullOrEmpty(export_user.PersonName) ? "" : export_user.PersonName.Replace("|", "").Trim();
                    exportItem.PERSONALNO = string.IsNullOrEmpty(export_user.PhoneNumber) ? "N/A" : export_user.PhoneNumber.Replace("|", "").Trim();
                    exportItem.EMAIL = string.IsNullOrEmpty(export_user.Email) ? "" : export_user.Email.Replace("|", "").Trim();
                    exportItem.VALIDITYDATE = export_user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod()).ToddMMyyyy();
                    exportItem.CLUSTERNAME = "N/A";
                    exportItem.DIVISIONNAME = "N/A";
                    exportItem.BRANCHNAME = groupname.Replace("|", "").Trim();
                    exportItem.DESIGNATION = "N/A";
                    //exportItem.ACCSTATUS = (export_user.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE : ((export_user.AccountStatus == BIOME.Constants.Account.AccountStatus.DEACTIVATED ? export_user.AccountStatus : BIOME.Constants.Account.AccountStatus.SUSPENDED)));
                    exportItem.ACCSTATUS = export_user.AccountStatus;
                    exportItem.ROLECODE = exportItem.ROLEDESC = String.Join("/", GetRolesForUser(export_user.Id).Select(t => t.ToProperRoleName()).ToList());// GetRolesForUser(activeUser.Id).ToList();
                    exportItem.ROLECODE = string.IsNullOrEmpty(exportItem.ROLECODE) ? "" : exportItem.ROLECODE.Replace("|", "").Trim();
                    exportItem.ROLEDESC = string.IsNullOrEmpty(exportItem.ROLEDESC) ? "" : exportItem.ROLEDESC.Replace("|", "").Trim();
                    exportItem.LASTDEACTIVATEDATE = export_user.LastDeActivateDate.Year > 2000 ? export_user.LastDeActivateDate.ToddMMyyyy() : "N/A";
                    exportItem.LASTSUSPENDDATE = export_user.LastSuspendDate.Year > 2000 ? export_user.LastSuspendDate.ToddMMyyyy() : "N/A";
                    exportItem.LASTLOGINDATE = export_user.DateLastLogin.Year > 2000 ? export_user.DateLastLogin.ToddMMyyyy() : "N/A";

                    //exportItem.LASTLOGINDATE = export_user.DateLastLogin.DateTime != DateTime.MinValue ? export_user.DateLastLogin.ToddMMyyyy() : "N/A";
                    //exportItem.LASTSUSPENDDATE = export_user.LastSuspendDate.Year > 2000 ? export_user.LastSuspendDate.ToddMMyyyy() : "N/A";
                    //exportItem.LASTDEACTIVATEDATE = export_user.LastDeActivateDate.Year > 2000 ? export_user.LastDeActivateDate.ToddMMyyyy() : "N/A";

                    exportItem.STARTDATE = export_user.CreatedAt.ToddMMyyyy();
                    exportItem.ENDDATE = exportItem.LASTDEACTIVATEDATE;

                    list.Add(exportItem);
                }
            }

            StringWriter sw = new StringWriter();
            var csv = new CsvWriter(sw);
            csv.Configuration.RegisterClassMap<NparksUserExportCSVMapper>();
            csv.Configuration.Encoding = Encoding.UTF8;
            csv.Configuration.Delimiter = "|";
            csv.WriteHeader<UserViewModel.NparksUserExport_ACE_CSVViewModel>();
            csv.WriteRecords(list);
            return sw.ToString();
            //return GetBytes(sw.ToString());
        }


        public ApplicationUser SetAccountDisablement(long userId, out bool isAlreadyInactive)
        {
            isAlreadyInactive = false;

            

            var user = userManager.FindById(userId);
            if (user == null)
            {
                return null;
            }

            string publicUserGroup = BIOME.Constants.Account.UserGroup.Public.ToLower();
            var isNotPublicUser = (user.Group == null || (user.Group != null && user.Group.Name.ToLower() != publicUserGroup));
            if (!isNotPublicUser) {
                return null;
            }
                


            if (!user.IsActive)
            {
                isAlreadyInactive = true;
                return null;
            }

            /*if (user.IsActive)
            {
                user.LastSuspendDate = DateTime.Now;
            }*/
            user.LastSuspendDate = DateTime.Now;
            bool roleRemoved = (user.Roles.Count() == 0);

            if (roleRemoved)
            {
                user.AccountStatus = Account.AccountStatus.DEACTIVATED;
            }
            else
            {
                user.AccountStatus = Account.AccountStatus.SUSPENDED;
            }

            user.IsActive = false;

            bool result = userManager.Update(user).Succeeded;
            if(result)
                return user;

            return null;
        }
        public ApplicationUser SetAccountDisableAndRemoveFromNparksUser(long userId, out bool isAlreadyRemove)
        {
            isAlreadyRemove = false;
            var user = userManager.FindById(userId);
            if (user == null)
            {
                return null;
            }

            if(user.Group!=null && user.Group.Name.Equals(BIOME.Constants.Account.UserGroup.Public, StringComparison.OrdinalIgnoreCase))
            {
                isAlreadyRemove = true;
                return null;
            }


            user.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            //bool isAllRemove = true;
            if (user.Roles.Any()) {
                var rolesToRemove = userManager.GetRoles(user.Id);
                
                /*var rolesToKeep = existingRoles.Intersect(updatedVM.RolesSelected);
                var rolesToRemove = existingRoles.Except(rolesToKeep);*/

                foreach (var role in rolesToRemove)
                {
                    userManager.RemoveFromRole(user.Id, role);

                    if (role == UserRoles.SiteManager && user.SitesManaging.Count > 0)
                    {
                        user.SitesManaging.Clear();
                    }

                }

               /* var rolesToAdd = updatedVM.RolesSelected.Except(rolesToKeep);

                foreach (var role in rolesToAdd)
                {
                    userManager.AddToRole(updatedVM.Id, role);
                }*/
            }

            if (user.IsActive)
            {
                user.LastSuspendDate = DateTime.Now;
            }
            user.LastDeActivateDate = DateTime.Now;
            user.AccountStatus = Account.AccountStatus.DEACTIVATED;
            /*if (isAllRemove)
            {
                user.AccountStatus = Account.AccountStatus.DEACTIVATED;
            }
            else
            {
                user.AccountStatus = Account.AccountStatus.SUSPENDED;
            }*/

            user.IsActive = false;

            bool result = userManager.Update(user).Succeeded;
            if (result)
                return user;

            return null;
        }
        public ApplicationUser RemoveRole(long userId, long roleId, out bool isAlreadyRemove,out bool isRoleExist)
        {
            isAlreadyRemove = false;
            isRoleExist = true;
            BIOMERole role = roleManager.FindById(roleId);
            if (role == null)
            {
                isRoleExist = false;
                return null;
            }

            var user = userManager.FindById(userId);
            if (user == null)
            {
                return null;
            }

            
            isAlreadyRemove = !user.Roles.Any(t => t.RoleId == roleId);
            if (isAlreadyRemove)
            {
                return null;
            }

            userManager.RemoveFromRole(user.Id, role.Name);
            if (role.Name == UserRoles.SiteManager && user.SitesManaging.Count > 0)
            {
                user.SitesManaging.Clear();
            }

            bool result = userManager.Update(user).Succeeded;
            if (result)
                return user;

            return null;
        }
        public byte[] ExportNparksUsersInfoForAuditBytes()
        {
            return GetBytes(ExportNparksUsersInfoForAudit());
        }
        public IQueryable<ApplicationUser> GetUsers(string[] roles = null, Group[] groups = null)
        {
            var users = userManager.Users;
            if (roles != null)
            {
                var rolesIds = roleManager.Roles.Where(r => roles.Contains(r.Name)).Select(r => r.Id);

                users = users.Where(u => u.Roles.Any(r => rolesIds.Contains(r.RoleId))).AsQueryable();
            }
            if (groups != null)
            {
                var groupsAndChildren = groupService.GetAllChildrenGroups(groups).Distinct().ToList();
                users = users.Where(u => groups.Any(g => groupsAndChildren.Contains(g)));
            }

            return users;
        }
        public List<ApplicationUser> GetAllUsers()
        {
            //return dbContext.Users.ToList();
            return dbContext.Users.ToList();
            

        }
        public List<ApplicationUser> GetAllUsersByGroup(long[] groupIds)
        {
            //return dbContext.Users.ToList();
            return dbContext.Users.Where(t=>t.Group!=null && groupIds.Contains(t.Group.Id)).ToList();


        }

        public ApplicationUser GetUserById(long id)
        {
            return userManager.FindById(id);
        }

        public ApplicationUser GetUserbyEmail(string email)
        {
            return userManager.FindByEmail(email);
        }
        public ApplicationUser GetUserbyUserName(string userName)
        {
            return userManager.FindByName(userName);
        }

        public async Task<ApplicationUser> GetUserByIdAsync(long id)
        {
            return await userManager.FindByIdAsync(id);
        }
        public List<ApplicationUser> GetUserByIdList(long[] id)
        {
            return dbContext.Users.Where(t => id.Contains(t.Id)).ToList();
        }

        public async Task<ApplicationUser> GetUserbyEmailAsync(string email)
        {
         
            return await userManager.FindByEmailAsync(email);
        }

       


        public ApplicationUser GetUserByFBId(string fbId)
        {
            return userManager.Find(new UserLoginInfo("Facebook", fbId));
        }

        public async Task<ApplicationUser> GetUserByFBIdAsync(string fbId)
        {
            return await userManager.FindAsync(new UserLoginInfo("Facebook", fbId));
        }

        public ApplicationUser GetUserByADId(string adId)
        {
            return userManager.Find(new UserLoginInfo("AD", adId));
        }

        public async Task<ApplicationUser> GetUserByADIdAsync(string adId)
        {
            return await userManager.FindAsync(new UserLoginInfo("AD", adId));
        }

        public IQueryable<BIOMERole> GetRoles()
        {
            return roleManager.Roles;
        }

        public IList<string> GetRolesForUser(long id)
        {
            return userManager.GetRoles(id);
        }

        public async Task<IList<string>> GetRolesForUserAsync(long id)
        {
            return await userManager.GetRolesAsync(id);
        }

        public bool SetAccountExpired(long id)
        {
            var user = userManager.FindById(id);
            if (user == null)
            {
                return true;
            }

            if (user.IsActive)
            {
                user.LastSuspendDate = DateTime.Now;
            }
            bool roleRemoved = (user.Roles.Count() == 0);

            if (roleRemoved)
            {
                user.AccountStatus = Account.AccountStatus.DEACTIVATED;
            }
            else
            {
                user.AccountStatus = Account.AccountStatus.SUSPENDED;
            }

            user.IsActive = false;

            


            logger.Error("Set User Inactive in UserService SetAccountExpired " + user.Id + " " + user.PersonName);
            return userManager.Update(user).Succeeded;
        }
        public async Task<ServiceResult> UpdateUserAsync(UserViewModel.DetailViewModel updatedVM)
        {
            var existingUser = await GetUserByIdAsync(updatedVM.Id);
            if (existingUser.IsActive && !updatedVM.IsActive)
            {
                logger.Error("Set User Inactive in UserService UpdateUserAsync " + existingUser.Id + " " + existingUser.PersonName);
            }

            Mapper.Map(updatedVM, existingUser);


            var existingRoles = userManager.GetRoles(updatedVM.Id);

            var rolesToKeep = existingRoles.Intersect(updatedVM.RolesSelected);

            var rolesToRemove = existingRoles.Except(rolesToKeep);

            foreach (var role in rolesToRemove)
            {
                await userManager.RemoveFromRoleAsync(updatedVM.Id, role);
            }

            var rolesToAdd = updatedVM.RolesSelected.Except(rolesToKeep);

            foreach (var role in rolesToAdd)
            {
                await userManager.AddToRoleAsync(updatedVM.Id, role);
            }

            if (updatedVM.UserGroupChanged.Count > 0)
            {
                var groupsSelected = updatedVM.UserGroupChanged.Select(ug => groupService.GetGroupById(ug));
                existingUser.Group = groupService.GetHighestGroupFromList(groupsSelected);
                //existingUser.Group = null;
            }
            else
            {
                var existingGroup = existingUser.Group;

                if (existingGroup != null)
                {
                    existingUser.Group = null;
                }


            }



            var updateResult = await userManager.UpdateAsync(existingUser);
            if (!updateResult.Succeeded)
            {
              //  return GenerateUserViewUseableErrors(existingUser.Email, existingUser.Email, updateResult.Errors);
            }

            return ServiceResult.Success;
        }

        public  ServiceResult UpdateUser(UserViewModel.DetailViewModel updatedVM)
        {
            var existingUser =  GetUserById(updatedVM.Id);
            /*if (existingUser.IsActive && !updatedVM.IsActive)
            {
                logger.Error("Set User Inactive in UserService UpdateUser " + existingUser.Id + " " + existingUser.FullName);
            }*/

            Mapper.Map(updatedVM, existingUser);


            var existingRoles = userManager.GetRoles(updatedVM.Id);

            var rolesToKeep = existingRoles.Intersect(updatedVM.RolesSelected);

            var rolesToRemove = existingRoles.Except(rolesToKeep);

            foreach (var role in rolesToRemove)
            {
                userManager.RemoveFromRole(updatedVM.Id, role);

                if (role == UserRoles.SiteManager)
                {
                    //Fixes for NPARK/BIOME/NCODE/2020_0165
                    if (existingUser.SitesManaging.Count > 0)
                    {
                        existingUser.SitesManaging.Clear();
                    }
                }

            }

            var rolesToAdd = updatedVM.RolesSelected.Except(rolesToKeep);

            foreach (var role in rolesToAdd)
            {
                 userManager.AddToRole(updatedVM.Id, role);
            }

            if (updatedVM.UserGroupChanged.Count > 0)
            {
                var groupsSelected = updatedVM.UserGroupChanged.Select(ug => groupService.GetGroupById(ug));
                existingUser.Group = groupService.GetHighestGroupFromList(groupsSelected);
                //existingUser.Group = null;
            }
            else
            {
                var existingGroup = existingUser.Group;

                if (existingGroup != null)
                {
                    existingUser.Group = null;
                }


            }



            var updateResult =  userManager.Update(existingUser);
            if (!updateResult.Succeeded)
            {
                //  return GenerateUserViewUseableErrors(existingUser.Email, existingUser.Email, updateResult.Errors);
                return ServiceResult.FailWithOutput("failed");
            }

            return ServiceResult.Success;
        }

        public int RemoveAllSitesToUser(long userId)
        {
            var currentSiteManager = GetUserById(userId);
            
            if (currentSiteManager == null)
            {
                return 0;
            }

            if (currentSiteManager.SitesManaging.Count > 0)
            {
                currentSiteManager.SitesManaging.Clear();
            }

            var updateTask = dbContext.SaveChanges();

            return updateTask;

        }

        public IPagedList GetUsersPagedList(int currentIndexPage, int pageSize, out int totalCount, string email = null)
        {
            var users = userManager.Users;

            if (!string.IsNullOrEmpty(email))
            {
                users = users.Where(u => u.Email.Contains(email) || u.PersonName.Contains(email));
            }

            totalCount = users.Count();

            var pagedUsers = users.OrderBy(u => u.CreatedAt).ToPagedList(currentIndexPage, pageSize);

            var userGroupById = new Dictionary<long, IEnumerable<string>>();

            foreach (var user in pagedUsers)
            {
                var groupsListing = groupService.GetGroupsHierarchyForGroup(user.Group);
                userGroupById.Add(user.Id, groupsListing.Select(g => g.GroupName));
            }

            var list = Mapper.Map<IPagedList<UserViewModel.ListViewModel>>(pagedUsers);

            foreach (var user in list)
            {
                user.UserGroup = string.Join(" > ", userGroupById[user.Id]);
            }

            return list;
        }

        public IEnumerable<ApplicationUser> GetSiteManagers()
        {
            var roleId = GetRoles().FirstOrDefault(r => r.Name == UserRoles.SiteManager).Id;
            return GetUsers().Where(u => u.Roles.Any(r => r.RoleId == roleId));
        }

        public void ScheduleMissingPasswordExpiredSendEmailJobs(Uri passwordResetLink, Uri contactUsLink)
        {
            var allPasswordExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t =>
                t.Task == BackgroundJobTask.TaskType.PasswordExpired ||
                t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification).ToList();
            
            var usersScheduled = new List<string>();
            var clearMissingUsersJobTasks = new List<string>();
            foreach (var task in allPasswordExpiredJobTasks)
            {
                if (task.Task == BackgroundJobTask.TaskType.PasswordExpired)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredFields>(task.Parameters);
                    if (!usersScheduled.Contains(parameters.Recipient))
                    {
                        usersScheduled.Add(parameters.Recipient);
                    }

                    if (GetUserbyEmail(parameters.Recipient) == null)
                    {
                        clearMissingUsersJobTasks.Add(task.JobId);
                        continue;
                    }
                }

                if (task.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredNotificationFields>(task.Parameters);
                    if (!usersScheduled.Contains(parameters.Recipient))
                    {
                        usersScheduled.Add(parameters.Recipient);
                    }

                    if (GetUserbyEmail(parameters.Recipient) == null)
                    {
                        clearMissingUsersJobTasks.Add(task.JobId);
                        continue;
                    }
                }
            }

            foreach (var jobId in clearMissingUsersJobTasks)
            {
                backgroundJobService.DeleteScheduledJob(jobId);
            }

            var usersToAddSchedule = GetUsers().Select(u => new UserViewModel.UserScheduleViewModel { Email = u.Email, DateActivate = u.DateActivate, DateLastLogin = u.DateLastLogin, DateLastChangePassword = u.DateLastChangePassword, PersonName = u.PersonName , group= u.Group.Name})
                .Where(u => !usersScheduled.Contains(u.Email) && u.DateActivate != new DateTimeOffset() && u.group.ToLower() !=   BIOME.Constants.Account.UserGroup.Public.ToLower());
            foreach (var user in usersToAddSchedule.ToList())
            {
                var userDetail = GetUserbyEmail(user.Email);

                if (userDetail.IsActive == false)
                {
                    continue;
                }

                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(systemParameterService.GetPasswordExpiryPeriod());

                passwordResetLink = passwordResetLink.ExtendQuery(new { email = user.Email });

                if (passwordExpireDate > DateTimeOffset.Now)
                {
                    backgroundJobService.SchedulePasswordExpired(
                                user.PersonName,
                                user.Email,
                                passwordExpireDate,
                                passwordResetLink,
                                contactUsLink,
                                new BackgroundJobService.BackgroundJobScheduleLaterDate()
                                {
                                    LaterDate = passwordExpireDate
                                });
                }

                if (passwordExpireDate.AddDays(-14) > DateTimeOffset.Now)
                {
                    backgroundJobService.SchedulePasswordExpiredNotification(
                                user.PersonName,
                                user.Email,
                                passwordExpireDate,
                                passwordResetLink,
                                contactUsLink,
                                new BackgroundJobService.BackgroundJobScheduleLaterDate()
                                {
                                    LaterDate = passwordExpireDate.AddDays(-14)
                                });
                }
            }
        }
        //public void ScheduleMissingAccountExpiredSendEmailJobs(Uri accountResetLink, Uri contactUsLink)
        //{
        //    var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t =>
        //        t.Task == BackgroundJobTask.TaskType.AccountExpired ||
        //        t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
        //    var clearMissingUsersJobTasks = new List<string>();

        //    var usersScheduled = new List<string>();
        //    foreach (var task in allAccountExpiredJobTasks)
        //    {
        //        if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
        //        {
        //            var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
        //            if (!usersScheduled.Contains(parameters.Recipient))
        //            {
        //                usersScheduled.Add(parameters.Recipient);
        //            }

        //            if (GetUserbyEmail(parameters.Recipient) == null)
        //            {
        //                clearMissingUsersJobTasks.Add(task.JobId);
        //                continue;
        //            }
        //        }

        //        if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
        //        {
        //            var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
        //            if (!usersScheduled.Contains(parameters.Recipient))
        //            {
        //                usersScheduled.Add(parameters.Recipient);
        //            }

        //            if (GetUserbyEmail(parameters.Recipient) == null)
        //            {
        //                clearMissingUsersJobTasks.Add(task.JobId);
        //                continue;
        //            }
        //        }
        //    }

        //    foreach (var jobId in clearMissingUsersJobTasks)
        //    {
        //        backgroundJobService.DeleteScheduledJob(jobId);
        //    }

        //    var usersToAddSchedule = GetUsers().Select(u => new UserViewModel.UserScheduleViewModel { Email = u.Email, DateActivate = u.DateActivate, DateLastLogin = u.DateLastLogin, DateLastChangePassword = u.DateLastChangePassword, FirstName = u.FirstName, LastName = u.LastName, PersonName = u.PersonName, group=(u.Group!=null? u.Group.Name :"") })
        //        .Where(u => !usersScheduled.Contains(u.Email) && u.DateActivate != new DateTimeOffset() && u.group.ToLower() != BIOME.Constants.Account.UserGroup.Public.ToLower());
        //    foreach (var user in usersToAddSchedule.ToList())
        //    {
        //        var userDetail = GetUserbyEmail(user.Email);

        //        if (userDetail.IsActive == false)
        //        {
        //            continue;
        //        }

        //        accountResetLink = accountResetLink.ExtendQuery(new { email = user.Email });

        //        var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetPasswordExpiryPeriod());
        //        if (accountExpireDate > DateTimeOffset.Now)
        //        {

        //            backgroundJobService.ScheduleAccountExpired(
        //                        user.FullName,
        //                        user.Email,
        //                        accountExpireDate,
        //                        accountResetLink,
        //                        contactUsLink,
        //                        new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //                        {
        //                            LaterDate = accountExpireDate
        //                        });
        //        }

        //        if (accountExpireDate.AddDays(-14) > DateTimeOffset.Now)
        //        {
        //            backgroundJobService.ScheduleAccountExpiredNotification(
        //                        user.FullName,
        //                        user.Email,
        //                        accountExpireDate,
        //                        accountResetLink,
        //                        contactUsLink,
        //                        new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //                        {
        //                            LaterDate = accountExpireDate.AddDays(-14)
        //                        });
        //        }
        //    }
        //}

        IList<ApplicationUser> IUserQueryService.Suggest(string name, string email, int limit)
        {
            var users = userManager.Users;

            return users.Where(u => u.Email.Contains(email) || u.PersonName.Contains(name))
                .Take(limit).ToList();
        }

        #endregion
    }

    public class UserService : UserServiceNoOWIN, IUserService
    {
        #region Fields

        private readonly ApplicationUserManager userManager;
        private readonly ApplicationRoleManager roleManager;
        private readonly IGroupService groupService;
        private readonly ApplicationSignInManager signInManager;
        private readonly IAuthenticationManager authenticationManager;
        private readonly ISystemParametersService systemParameterService;
        private readonly IBadgesService badgesService;
        private readonly IEmailService emailService;
        private readonly IAuditTrailService auditTrailService;
        private readonly IDeveloperInfoService developerInfoService;
        private readonly IBackgroundJobService backgroundJobService;
        private readonly ApplicationDbContext dbContext;
        private readonly ITokenService tokenService;

        private static readonly log4net.ILog logger = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        #endregion

        #region Constructors

        public UserService(
            ApplicationUserManager userManager,
            ApplicationRoleManager roleManager,
            IGroupService groupService,
            ApplicationSignInManager signInManager,
            IAuthenticationManager authenticationManager,
            ISystemParametersService systemParameterService,
            IBadgesService badgesService,
            IEmailService emailService,
            IAuditTrailService auditTrailService,
            IDeveloperInfoService developerInfoService,
            IBackgroundJobService backgroundJobService,
            ITokenService tokenService,
            ApplicationDbContext dbContext
            //) : base(userManager, roleManager, groupService, systemParameterService, badgesService, backgroundJobService, emailService, auditTrailService, dbContext)
            ) : base(userManager, roleManager, groupService, systemParameterService, backgroundJobService, dbContext)
        {
            this.userManager = userManager;
            this.roleManager = roleManager;
            this.groupService = groupService;
            this.signInManager = signInManager;
            this.authenticationManager = authenticationManager;
            this.systemParameterService = systemParameterService;
            this.badgesService = badgesService;
            this.emailService = emailService;
            this.auditTrailService = auditTrailService;
            this.developerInfoService = developerInfoService;
            this.backgroundJobService = backgroundJobService;
            this.tokenService = tokenService;
            this.dbContext = dbContext;
        }

        #endregion

        #region Public Methods

        public async Task<bool> IsEmailConfirmedAsync(long id)
        {
            return await userManager.IsEmailConfirmedAsync(id);
        }

        public async Task<ServiceResult> LoginAsync(AccountViewModel.LoginViewModel loginVM, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink)
        {
            var user = GetUserbyEmail(loginVM.EmailOrSOEID);
            if(user == null)
            {
                //return ServiceResult.Failed(new ServiceError() { Code = 1036 });
                return ServiceResult.Failed(new ServiceError() { Code = 1001 }); //fix for NPARK/BIOME/NCODE/2020_0189
            }
            DynamicPasswordCipher.IV = user.IV;
            DynamicPasswordCipher.Key = user.Key;
            var loggedinUser = await userManager.FindAsync(loginVM.EmailOrSOEID, loginVM.Password);
            if (loggedinUser != null)
            {
                if (!loggedinUser.EmailConfirmed)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 1108 });
                }

                Logout();

            }

            var userByEmail = GetUserbyEmail(loginVM.EmailOrSOEID);

            if (userByEmail != null)
            {
                if (userByEmail.AccessFailedCount > 4)
                {
                    // Error code for user password tried more than 5 times
                    return ServiceResult.Failed(new ServiceError() { Code = 1034 });
                }
                //CR3&CR4 Phase1
                if (userByEmail.Roles.Count() == 0)
                {
                    //Account without role
                    return ServiceResult.Failed(new ServiceError() { Code = 1109 });
                }
            }

            int platform_id = 0;
            if (userByEmail != null) {
                platform_id = tokenService.CheckCurrentLogin(userByEmail.Id);
            }
                

            List<int> errorCodes = new List<int>();
            var signInStatus = await signInManager.PasswordSignInAsync(loginVM.EmailOrSOEID, loginVM.Password, true, true);
            switch (signInStatus)
            {
                case SignInStatus.Success:
                    break;
                case SignInStatus.Failure:
                    errorCodes.Add(1001);
                    break;
                default:
                    errorCodes.Add(1001);
                    break;
            }

            //string ip = HttpContext.Current.Request.UserHostAddress;
            string ip = GetIP(true);
            if (errorCodes.Count > 0)
            {
                if (loggedinUser == null)
                {
                    loggedinUser = GetUserbyEmail(loginVM.EmailOrSOEID);

                }

                var serviceErrors = errorCodes.Select(c => new ServiceError() { Code = c });
                auditTrailService.LogAuditTrail(loggedinUser != null ? loggedinUser.Id : 0, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Unsuccessfully, Code: 1001");
                return ServiceResult.Failed(serviceErrors.ToArray());
            }

            if (await HasAccountExpiredAsync(loggedinUser.Id))
            {
                auditTrailService.LogAuditTrail(loggedinUser.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Unsuccessfully, AccountExpired");
                return ServiceResult.Failed(new ServiceError() { Code = 1003 });
            }
            
            auditTrailService.LogAuditTrail(loggedinUser.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Successfully");

            var signedInUser = await userManager.FindByEmailAsync(loginVM.EmailOrSOEID);
            signedInUser.IsFirstLogin = false;
            signedInUser.DateLastLogin = DateTimeOffset.Now;
            signedInUser.AccessFailedCount = 0;
            signedInUser.SessionID = HttpContext.Current.Session.SessionID;
            signedInUser.LastLoginType = Constants.Account.LoginType.LoginType_BIOME_Account;

            var updateResult = await userManager.UpdateAsync(signedInUser);

            tokenService.AddConCurrentAlertMessage(loggedinUser.Id, platform_id, TokenService.PLATFORM_ID_BIOME,contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(loggedinUser.Id);
            
            
            //var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(signedInUser.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
            //foreach (var task in allAccountExpiredJobTasks)
            //{
            //    if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
            //        if (parameters.Recipient == signedInUser.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //    else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
            //        if (parameters.Recipient == signedInUser.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //}

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> LoginOTPAsync(long userId, string otpCode)
        {
            bool validOTP = emailService.IsValidEmailOTP(userId, otpCode);
            if (!validOTP)
            {
                auditTrailService.LogAuditTrail(userId, GetIP(true), (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website OTP Login Unsuccessfully, Code: 1035");
                return ServiceResult.Failed(new ServiceError() { Code = 1035 });
            }
            //string ip = HttpContext.Current.Request.UserHostAddress;
            string ip = GetIP(true);
            auditTrailService.LogAuditTrail(userId, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Admin Portal OTP Login Successfully");

            var signedInUser = await userManager.FindByIdAsync(userId);
            signedInUser.IsFirstLogin = false;
            signedInUser.DateLastLogin = DateTimeOffset.Now;
            signedInUser.AccessFailedCount = 0;
            signedInUser.SessionID = HttpContext.Current.Session.SessionID;
            var updateResult = await userManager.UpdateAsync(signedInUser);

           

            return ServiceResult.Success;
        }
        //CR3&CR4 Phase1
        public async Task<ServiceResult> LoginAPIFBAsync(FB_LoginInfo extLoginInfo, string developerId)
        {
            //string ip = HttpContext.Current.Request.UserHostAddress;
            string ip = GetIP(true);
            //var user = await userManager.FindAsync(extLoginInfo.Login);
            var user = await userManager.FindByEmailAsync(extLoginInfo.Email);
            if (user == null)
            {
                return ServiceResult.Failed(
                    new ServiceError() { Code = Constants.Account.API.AccountStatus.UserNotFound },
                    new ServiceError() { Code = 1101, Parameters = new List<string>() { extLoginInfo.FBAppId } });
            }

            if (user.Roles.Count() == 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.AccountWithoutRole });
            }
            if (!user.EmailConfirmed)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1108 });
            }

            //Test account expire
            if (await HasAccountExpiredAsync(user.Id))
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "APIFacebook Login Unsuccessfully, AccountExpired",true);
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.AccountExpired });
            }

            
            //Test account locked
            if (await userManager.IsLockedOutAsync(user.Id))
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "APIFacebook Login Unsuccessfully, AccountLocked",true);
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.Locked });
            }

            //Increase account login, valid from
            user.DateLastLogin = DateTimeOffset.Now;
            user.LastLoginType = Constants.Account.LoginType.LoginType_Facebook;
            var updateResult = await userManager.UpdateAsync(user);

            auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "APIFacebook Login Successful",true);

            return ServiceResult.Success;
        }
        public async Task<ServiceResult> LoginFBAsync(AccountViewModel.LoginViewModel loginVM, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink)
        {
            var user = GetUserbyEmail(loginVM.EmailOrSOEID);
            if (user == null)
            {
                //return ServiceResult.Failed(new ServiceError() { Code = 1036 }); fix for NPARK/BIOME/NCODE/2020_0189
                return ServiceResult.Failed(new ServiceError() { Code = 1001 });
            }

            if (!user.EmailConfirmed)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1108 });
            }


            if (user.AccessFailedCount > 4)
            {
                // Error code for user password tried more than 5 times
                return ServiceResult.Failed(new ServiceError() { Code = 1034 });
            }
            //CR3&CR4 Phase1
            if (user.Roles.Count() == 0)
            {
                //Account without role
                return ServiceResult.Failed(new ServiceError() { Code = 1109 });
            }

            string ip = GetIP(true);

            if (await HasAccountExpiredAsync(user.Id))
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Unsuccessfully, AccountExpired (Facebook)",true);
                return ServiceResult.Failed(new ServiceError() { Code = 1003 });
            }

            //

            int platform_id = tokenService.CheckCurrentLogin(user.Id);
            

            await signInManager.SignInAsync(user, isPersistent: true, rememberBrowser: false);
            user.SessionID = HttpContext.Current.Session.SessionID;
            user.LastLoginType = Constants.Account.LoginType.LoginType_Facebook;

            var updateResult = await userManager.UpdateAsync(user);
            auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Successfully (Facebook)",true);

            tokenService.AddConCurrentAlertMessage(user.Id, platform_id, TokenService.PLATFORM_ID_BIOME,contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(user.Id);

            //var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
            //foreach (var task in allAccountExpiredJobTasks)
            //{
            //    if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //    else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //}

            //string userGroupName = "";
            //if (user.Group != null)
            //{
            //    userGroupName = user.Group.Name.ToLower();
            //}
            //if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            //{
            //    var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
            //    backgroundJobService.ScheduleAccountExpired(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate
            //        });
            //    backgroundJobService.ScheduleAccountExpiredNotification(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate.AddDays(-14)
            //        });
            //}
            //

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> LoginAPIAsync(string email, string passwordInHMAC, string developerId)
        {
            //string ip = HttpContext.Current.Request.UserHostAddress;
            string ip = GetIP(true);

            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ServiceResult.Failed(
                    new ServiceError() { Code = Constants.Account.API.AccountStatus.UserNotFoundOrInactive },
                    new ServiceError() { Code = 1101, Parameters = new List<string>() { email } });
            }

            if (!user.EmailConfirmed)
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "API Login Unsuccessfully, NotEmailConfirmed",true);
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.NotEmailConfirmed });
            }
            if (user.Roles.Count() == 0)
            {
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.AccountWithoutRole });
            }

            // Get encryption key and IV from appSettings in web.config
            string encryptionKey = System.Configuration.ConfigurationManager.AppSettings["API_Encryption_Key"];
            string encryptionIV = System.Configuration.ConfigurationManager.AppSettings["API_Encryption_IV"];

            if (string.IsNullOrEmpty(encryptionKey) || string.IsNullOrEmpty(encryptionIV))
            {
                logger.Error("API_Encryption_Key or API_Encryption_IV is missing from appSettings in web.config");
                return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.InvalidLogin });
            }

            var passwordHasher = new BIOMEPasswordHasher();
            
            try
            {
                // Decrypt the base64 encoded password using the key and IV from web.config
                byte[] encryptedPasswordBytes;
                try
                {
                    encryptedPasswordBytes = Convert.FromBase64String(passwordInHMAC);
                }
                catch (FormatException)
                {
                    logger.Error("Invalid base64 string provided for passwordInHMAC");
                    return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.InvalidLogin });
                }
                
                byte[] keyBytes = Convert.FromBase64String(encryptionKey);
                byte[] ivBytes = Convert.FromBase64String(encryptionIV);

                string decryptedPassword = passwordHasher.Decrypt(encryptedPasswordBytes, keyBytes, ivBytes);
                
                // Verify the decrypted password against the stored hash
                var passwordVerificationResult = passwordHasher.VerifyHashedPassword(user.PasswordHash, decryptedPassword);
                
                if (passwordVerificationResult == Microsoft.AspNet.Identity.PasswordVerificationResult.Success)
                {
                    //Test account expire
                    if (await HasAccountExpiredAsync(user.Id))
                    {
                        auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "API Login Unsuccessfully, AccountExpired",true);
                        return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.AccountExpired });
                    }

                    //Test account locked
                    if (await userManager.IsLockedOutAsync(user.Id))
                    {
                        auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "API Login Unsuccessfully, AccountLocked",true);
                        return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.Locked });
                    }

                    //Increase account login, valid from
                    user.DateLastLogin = DateTimeOffset.Now;
                    user.LastLoginType = Constants.Account.LoginType.LoginType_BIOME_Account;
                    user.AccessFailedCount = 0;
                    var updateResult = await userManager.UpdateAsync(user);

                    auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "API Login Successful",true);

                    return ServiceResult.Success;
                }
            }
            catch (Exception ex)
            {
                logger.Error("Error during API login password verification", ex);
            }

            auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "API Login Unsuccessfully, InvalidLogin",true);
            return ServiceResult.Failed(new ServiceError() { Code = Constants.Account.API.AccountStatus.InvalidLogin });
        }

        public async Task<ServiceResult> ADSignInAsync(ApplicationUser user, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink, bool isPersistent = false)
        {

            int platform_id = tokenService.CheckCurrentLogin(user.Id);
            await signInManager.SignInAsync(user, isPersistent, false);

            user.IsFirstLogin = false;
            user.DateLastLogin = DateTimeOffset.Now;
            user.AccessFailedCount = 0;
            user.SessionID = HttpContext.Current.Session.SessionID;
            user.LastLoginType = Constants.Account.LoginType.LoginType_AD;
            var updateResult = await userManager.UpdateAsync(user);

            string ip = GetIP(true);
            if (!string.IsNullOrEmpty(ip))
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Successfully (AD)");
            }

            tokenService.AddConCurrentAlertMessage(user.Id, platform_id, TokenService.PLATFORM_ID_BIOME, contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(user.Id);

            //var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
            //foreach (var task in allAccountExpiredJobTasks)
            //{
            //    if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //    else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //}
            //string userGroupName = "";
            //if (user.Group !=null)
            //{
            //    userGroupName = user.Group.Name.ToLower();
            //}
            //if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            //{
            //    var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
            //    backgroundJobService.ScheduleAccountExpired(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate
            //        });
            //    backgroundJobService.ScheduleAccountExpiredNotification(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate.AddDays(-14)
            //        });
            //}
            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ADFSSignInAsync(ApplicationUser user, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink, bool isPersistent = false)
        {

            //Since ADFS is auto signin, can't display alert message.
            tokenService.RemoveConCurrentAlertMessage(user.Id, TokenService.CONCURRENT_ALERT_MESSAGE_1ST_LOGIN);

            int platform_id = tokenService.CheckCurrentLogin(user.Id);
            
            await signInManager.SignInAsync(user, isPersistent, false);

            user.IsFirstLogin = false;
            user.DateLastLogin = DateTimeOffset.Now;
            user.AccessFailedCount = 0;
            user.SessionID = HttpContext.Current.Session.SessionID;
            user.LastLoginType = Constants.Account.LoginType.LoginType_ADFS;
            var updateResult = await userManager.UpdateAsync(user);

            string ip = GetIP(true);
            if (!string.IsNullOrEmpty(ip))
            {
                auditTrailService.LogAuditTrail(user.Id, ip, (int)BIOME.Enumerations.Audit.Action.Login, "User", "Website Login Successfully (ADFS)");
            }

            tokenService.AddConCurrentAlertMessage(user.Id, platform_id, TokenService.PLATFORM_ID_BIOME,contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(user.Id);
            return ServiceResult.Success;
        }

        

        //public async Task<ServiceResult> ExternalSignInAsync(Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink, bool isPersistent = false)
        //{
        //    var loginInfo = await GetExternalLoginInfoAsync();
        //    if (loginInfo == null)
        //    {
        //        return ServiceResult.Failed(new ServiceError() { Code = 1001 });
        //    }

        //    // Sign in the user with this external login provider if the user already has a login
        //    var result = await signInManager.ExternalSignInAsync(loginInfo, isPersistent: true);
        //    switch (result)
        //    {
        //        case SignInStatus.Success:
        //            var user = await userManager.FindByEmailAsync(loginInfo.Email);
        //            if (user != null)
        //            {
        //                user.SessionID = HttpContext.Current.Session.SessionID;
        //                user.DateLastLogin = DateTimeOffset.Now;
        //                var updateResult = await userManager.UpdateAsync(user);

        //                var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
        //                foreach (var task in allAccountExpiredJobTasks)
        //                {
        //                    if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
        //                    {
        //                        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
        //                        if (parameters.Recipient == user.Email)
        //                        {
        //                            backgroundJobService.DeleteScheduledJob(task.JobId);
        //                        }
        //                    }
        //                    else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
        //                    {
        //                        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
        //                        if (parameters.Recipient == user.Email)
        //                        {
        //                            backgroundJobService.DeleteScheduledJob(task.JobId);
        //                        }
        //                    }
        //                }

        //                var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
        //                backgroundJobService.ScheduleAccountExpired(
        //                    user.FullName,
        //                    user.Email,
        //                    accountExpireDate,
        //                    accountResetLink,
        //                    contactUsLink,
        //                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //                    {
        //                        LaterDate = accountExpireDate
        //                    });
        //                backgroundJobService.ScheduleAccountExpiredNotification(
        //                    user.FullName,
        //                    user.Email,
        //                    accountExpireDate,
        //                    accountResetLink,
        //                    contactUsLink,
        //                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //                    {
        //                        LaterDate = accountExpireDate.AddDays(-14)
        //                    });
        //            }
        //            return ServiceResult.Success;
        //        case SignInStatus.LockedOut:
        //            return ServiceResult.Failed(new ServiceError() { Code = 1104 });
        //        case SignInStatus.RequiresVerification:
        //            return ServiceResult.Failed(new ServiceError() { Code = 1105 });
        //        case SignInStatus.Failure:
        //        default:
        //            // If the user does not have an account, then prompt the user to create an account
        //            return ServiceResult.Failed(
        //                new ServiceError() { Code = 1106, ModelPropertyName = "Provider", Parameters = new List<string>() { loginInfo.Login.LoginProvider } },
        //                new ServiceError() { Code = 1106, ModelPropertyName = "Id", Parameters = new List<string>() { loginInfo.ExternalIdentity.GetUserId() } },
        //                new ServiceError() { Code = 1106, ModelPropertyName = "Email", Parameters = new List<string>() { loginInfo.Email } },
        //                new ServiceError() { Code = 1106, ModelPropertyName = "First Name", Parameters = new List<string>() { loginInfo.ExternalIdentity.FindFirstValue(ClaimTypes.GivenName) } },
        //                new ServiceError() { Code = 1106, ModelPropertyName = "Last Name", Parameters = new List<string>() { loginInfo.ExternalIdentity.FindFirstValue(ClaimTypes.Surname) } });
        //    }
        //}

        //public async Task<ExternalLoginInfo> GetExternalLoginInfoAsync()
        //{
        //    try
        //    {
        //        var loginInfo = await authenticationManager.GetExternalLoginInfoAsync();

        //        if (loginInfo != null && loginInfo.Login.LoginProvider == "Facebook")
        //        {
        //            var identity = authenticationManager.GetExternalIdentity(DefaultAuthenticationTypes.ExternalCookie);
        //            var access_token = identity.FindFirstValue("FacebookAccessToken");
        //            var email = identity.FindFirstValue("urn:facebook:email");
        //            var first_name = identity.FindFirstValue("urn:facebook:first_name");
        //            var last_name = identity.FindFirstValue("urn:facebook:last_name");

        //            loginInfo.Email = email;
        //            loginInfo.DefaultUserName = email;
        //            loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Email, email));
        //            loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.GivenName, first_name));
        //            loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Surname, last_name));
        //            loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Name, loginInfo.ExternalIdentity.Name));
        //        }

        //        return loginInfo;
        //    } catch(Exception e)
        //    {
        //        logger.Error("erorr :" + e.Message);
        //        return null;
        //    }
        //}
        private class FB_TokenInfoData
        {
            public FB_TokenInfo data { get; set; }
        }
        private class FB_TokenInfo
        {
            public string access_token { get; set; } //long-lived-user-access-token
            public string token_type { get; set; } //bearer
            public string expires_in { get; set; } //The number of seconds until the token expires


        }

        private class FB_UserInfoData
        {
            public FB_UserInfo data { get; set; }
        }
        private class FB_UserInfo
        {
            public string id { get; set; }
            public string email { get; set; }
            public string first_name { get; set; }
            public string last_name { get; set; }
            public string name { get; set; }

        }
        public FB_LoginInfo GetFacebookLoginInfo(string accessToken)
        {
            FB_LoginInfo loginInfo=null;
            bool isValidFBToken = false;
            using (var client = new HttpClient())
            {

                string FB_UserID = "";
                string FB_email = "";
                string FB_firstName = "";
                string FB_lastName = "";
                string FBAppID = "";

                //string FBURL = "http://graph.facebook.com/v8.0/";
                var FacebookAPI = ConfigurationManager.AppSettings["FacebookAPI"];
                string FBURL = FacebookAPI + "/v8.0/";
                try
                {
                    FBAppID = ConfigurationManager.AppSettings["fbAppId"];
                    string FBAppSecret = ConfigurationManager.AppSettings["fbAppSecret"];

                    var urlToGenLongValidToken = client.GetStringAsync(FBURL + "oauth/access_token?grant_type=fb_exchange_token&client_id=" + FBAppID + "&client_secret=" + FBAppSecret + "&fb_exchange_token=" + accessToken).Result;
                    
                    FB_TokenInfo fb_tokenInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<FB_TokenInfo>(urlToGenLongValidToken);

                    if (fb_tokenInfo != null && fb_tokenInfo.access_token != null && fb_tokenInfo.token_type.Equals("bearer"))
                    {
                        var responseString = client.GetStringAsync(FBURL + "me?access_token=" + fb_tokenInfo.access_token + "&fields=id,first_name,last_name,email,name&locale=en_US").Result;
                        FB_UserInfo fb_userInfo = Newtonsoft.Json.JsonConvert.DeserializeObject<FB_UserInfo>(responseString);
                        if (fb_userInfo != null && !string.IsNullOrEmpty(fb_userInfo.email))
                        {
                            FB_email = fb_userInfo.email;
                            FB_firstName = fb_userInfo.first_name;
                            FB_lastName = fb_userInfo.last_name;
                            FB_UserID = fb_userInfo.id;

                            isValidFBToken = true;
                        }
                    }

                }
                catch (Exception ex)
                {
                    logger.Error(FBURL + " " + ex.ToString());
                    isValidFBToken = false;
                }

                if (isValidFBToken)
                {
                    loginInfo = new FB_LoginInfo();
                    loginInfo.Email = FB_email;
                    loginInfo.First_name = FB_firstName;
                    loginInfo.Last_name = FB_lastName;
                    loginInfo.ID = FB_UserID;
                    loginInfo.FBAppId = FBAppID;

                }
            }
            return loginInfo;

            //var loginInfo = new ExternalLoginInfo();
            //var fb = new FacebookClient(accessToken);
            //fb.SetHttpWebRequestFactory(uri =>
            //{
            //    var request = new HttpWebRequestWrapper((HttpWebRequest)WebRequest.Create(uri));
            //    var proxy = ConfigurationManager.AppSettings[BIOME.Constants.Configuration.Server.AppSettings.Proxy];
            //    if (!string.IsNullOrEmpty(proxy))
            //    {
            //        request.Proxy = new WebProxy(proxy, true);
            //    }
            //    return request;
            //});
            //dynamic myInfo = fb.Get("/me?fields=email,first_name,last_name,name"); // specify the email field
            //loginInfo.Email = myInfo.email;
            //loginInfo.DefaultUserName = myInfo.email;
            //loginInfo.ExternalIdentity = new ClaimsIdentity("ExternalLogin");
            //loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Email, myInfo.email));
            //loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.GivenName, myInfo.first_name));
            //loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Surname, myInfo.last_name));
            //loginInfo.ExternalIdentity.AddClaim(new Claim(ClaimTypes.Name, myInfo.name));
            //loginInfo.Login = new UserLoginInfo("Facebook", myInfo.id);
            //return loginInfo;
        }

        public class ReCaptchaResponse
        {
            public bool success
            {
                get;
                set;
            }
            public string challenge_ts
            {
                get;
                set;
            }
            public string hostname
            {
                get;
                set;
            }
            [JsonProperty(PropertyName = "error-codes")]
            public List<string> error_codes
            {
                get;
                set;
            }
        }
        public class ReCaptchaEnterprisePost
        {
            [JsonProperty(PropertyName = "event")]
            public ReCaptchaEnterprisePostEvent event_obj
            {
                get;
                set;
            }
          
        }
        public class ReCaptchaEnterprisePostEvent
        {
           
            public string token
            {
                get;
                set;
            }
            public string siteKey
            {
                get;
                set;
            }
            public string expectedAction
            {
                get;
                set;
            }

        }
        public class ReCaptchaEnterpriseResponse
        {
            public ReCaptchaEnterpriseResponsetokenProperties tokenProperties
            {
                get;
                set;
            }
            public ReCaptchaEnterpriseResponseError error
            {
                get;
                set;
            }
        }
        public class ReCaptchaEnterpriseResponsetokenProperties
        {
            public bool valid
            {
                get;
                set;
            }
            public string invalidReason
            {
                get;
                set;
            }
            public string androidPackageName
            {
                get;
                set;
            }
            public string iosBundleId
            {
                get;
                set;
            }

        }
        public class ReCaptchaEnterpriseResponseError
        {
            public int code
            {
                get;
                set;
            }
            public string message
            {
                get;
                set;
            }
            public string status
            {
                get;
                set;
            }
           

        }
        public class ReCaptchaEnterpriseVerifyResponse
        {
            //1: token valid, 2: token invalid with reason, 3: connection issue to GoogleReCAPTCHA
            public int code
            {
                get;
                set;
            }
            public string ErrorDetails
            {
                get;
                set;
            }
            

        }
        public int VerifyGoogleReCaptcha(string responseToken) //Recaptcha CR
        {
            int result = 0;
            using (var client = new HttpClient())
            {

                var siteAPIURL = ConfigurationManager.AppSettings["GoogleSiteAPI"];
                try
                {

                    bool isGoogleRecaptchaServerSideVerify = true;
                    bool.TryParse(ConfigurationManager.AppSettings["GoogleRecaptchaServerSideVerify"],out isGoogleRecaptchaServerSideVerify);

                    if (!isGoogleRecaptchaServerSideVerify)
                    {
                        result = 1;
                        return result;
                    }
                    
                    string secretKey = ConfigurationManager.AppSettings["GoogleRecaptchaSecretKey"];

                    var urlToGenLongValidToken = client.GetStringAsync(siteAPIURL + "?secret=" + secretKey + "&response=" + responseToken).Result;

                    ReCaptchaResponse reCaptchaResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<ReCaptchaResponse>(urlToGenLongValidToken);

                    if (reCaptchaResponse != null)
                    {
                        if (reCaptchaResponse.success)
                        {
                            result = 1;
                        }
                        else {

                            logger.Error(siteAPIURL + " Failed.");

                            if (reCaptchaResponse.error_codes.Count > 0) {
                                logger.Error(siteAPIURL + " error_code: " + string.Join(",", reCaptchaResponse.error_codes.ToArray()));
                                bool secrectKeyIssue = reCaptchaResponse.error_codes.Contains("missing-input-secret") || reCaptchaResponse.error_codes.Contains("invalid-input-secret");
                                if(secrectKeyIssue)
                                    result = 3;
                                
                            }

                            
                        }
                    }
                }
                catch (Exception ex)
                {
                    logger.Error(siteAPIURL + " " + ex.ToString());
                    result = 2;
                }

             
            }
            return result;

           
        }

        public ReCaptchaEnterpriseVerifyResponse VerifyGoogleReCaptchaEnterprise_SGBioAtlas(string responseToken,string expectedAction,string platformID) //Recaptcha CR SGBioAtlas
        {
            ReCaptchaEnterpriseVerifyResponse reCaptchaEnterpriseVerifyResponse = new ReCaptchaEnterpriseVerifyResponse();
            if (string.IsNullOrEmpty(responseToken)) {
                reCaptchaEnterpriseVerifyResponse.code = 2; //token error
                reCaptchaEnterpriseVerifyResponse.ErrorDetails = "ReCAPTCHA token required.";
                return reCaptchaEnterpriseVerifyResponse;
            }
            using (var client = new HttpClient())
            {

                
                try
                {
                    var siteAPIURL = ConfigurationManager.AppSettings["GoogleRecaptchaEnterpriseAPI"];
                    bool isGoogleRecaptchaServerSideVerify = true;
                    bool.TryParse(ConfigurationManager.AppSettings["GoogleRecaptchaEnterpriseServerSideVerify"], out isGoogleRecaptchaServerSideVerify);

                    if (!isGoogleRecaptchaServerSideVerify)
                    {
                        reCaptchaEnterpriseVerifyResponse.code =1;
                        return reCaptchaEnterpriseVerifyResponse;
                    }

                    string apiKey = ConfigurationManager.AppSettings["GoogleRecaptchaEnterpriseAPIKey"];
                    string secretKey = "";

                    if (platformID == TokenService.API_PLATFORM_ANDROID)
                        secretKey = ConfigurationManager.AppSettings["GoogleRecaptchaEnterpriseSiteKeyAndroid"];
                    else if (platformID == TokenService.API_PLATFORM_IOS)
                        secretKey = ConfigurationManager.AppSettings["GoogleRecaptchaEnterpriseSiteKeyiOS"];


                    if (string.IsNullOrEmpty(secretKey))
                    {
                        reCaptchaEnterpriseVerifyResponse.code = 3; //connection error
                        reCaptchaEnterpriseVerifyResponse.ErrorDetails = "SiteKey Required.";
                        return reCaptchaEnterpriseVerifyResponse;
                    }

                    //var urlToGenLongValidToken = client.GetStringAsync(siteAPIURL + "?secret=" + secretKey + "&response=" + responseToken).Result;
                    var apiurl = siteAPIURL + "?key=" + apiKey;
                    ReCaptchaEnterprisePost reCaptchaEnterprisePost = new ReCaptchaEnterprisePost();
                    reCaptchaEnterprisePost.event_obj = new ReCaptchaEnterprisePostEvent() { siteKey = secretKey,token=responseToken,expectedAction= expectedAction };
                    var responseHttp = client.PostAsJsonAsync(apiurl, reCaptchaEnterprisePost).Result;
                    if (!responseHttp.IsSuccessStatusCode) {
                        reCaptchaEnterpriseVerifyResponse.code = 3; //connection error
                        reCaptchaEnterpriseVerifyResponse.ErrorDetails = "Unable to connect to Google ReCAPTCHA server. httpstatus: " + responseHttp.StatusCode;
                        return reCaptchaEnterpriseVerifyResponse;
                    }

                    var responseResult = responseHttp.Content.ReadAsStringAsync().Result;

                    ReCaptchaEnterpriseResponse reCaptchaResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<ReCaptchaEnterpriseResponse>(responseResult);

                    if (reCaptchaResponse != null)
                    {
                        if (reCaptchaResponse.error != null)
                        {
                            reCaptchaEnterpriseVerifyResponse.code = 3; //connection error
                            if (reCaptchaResponse.error!=null)
                            {
                                reCaptchaEnterpriseVerifyResponse.ErrorDetails = "Error: " + reCaptchaResponse.error.ToJson();
                                //logger.Error(siteAPIURL + " " + reCaptchaEnterpriseVerifyResponse.ErrorDetails);
                                

                            }
                        }
                        else 
                        {
                            if (reCaptchaResponse.tokenProperties.valid)
                            {
                                reCaptchaEnterpriseVerifyResponse.code = 1;
                            }
                            else
                            {
                                reCaptchaEnterpriseVerifyResponse.code = 2; //token error
                                reCaptchaEnterpriseVerifyResponse.ErrorDetails = "ReCAPTCHA token invalid. Invalid Reason: " + reCaptchaResponse.tokenProperties.invalidReason;
                                //logger.Error(siteAPIURL + " " + reCaptchaEnterpriseVerifyResponse.ErrorDetails);
                            }
                        }

                        
                    }
                }
                catch (Exception ex)
                {
                    reCaptchaEnterpriseVerifyResponse.code = 3; //connection error
                    reCaptchaEnterpriseVerifyResponse.ErrorDetails = "Exception occurred. Please check the error log file for details.";
                    logger.Error("VerifyGoogleReCaptchaEnterprise_SGBioAtlas: " + ex.ToString());
                    
                }


            }
            return reCaptchaEnterpriseVerifyResponse;


        }

        //CR3&CR4 Phase1
        public async Task<ServiceResult> FacebookRegistrationOrMappingAsync(AccountViewModel.ActivateAccountExtViewModel signupExtVM)
        {
            //

            var newUser = Mapper.Map<ApplicationUser>(signupExtVM);
            newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            newUser.EmailConfirmed = true;
            newUser.IsActive = true;
            newUser.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
            
            newUser.DateActivate = DateTime.Now;
            //DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
            //DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
            //newUser.IV = DynamicPasswordCipher.IV;
            //newUser.Key = DynamicPasswordCipher.Key;
            var signupResult = await userManager.CreateAsync(newUser);
            if (!signupResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(signupExtVM.Email, signupExtVM.Email, signupResult.Errors);
            }
            
            newUser = await userManager.FindByEmailAsync(signupExtVM.Email);
            if (newUser == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { signupExtVM.Email } });
            }

            //var addPasswordResult = await userManager.AddPasswordAsync(newUser.Id, signupExtVM.Password);
            var addRoleResult = await userManager.AddToRoleAsync(newUser.Id, UserRoles.Public);

            //var info = await GetExternalLoginInfoAsync();
            var addLoginResult = await userManager.AddLoginAsync(newUser.Id, new UserLoginInfo("Facebook", signupExtVM.ExternalUserId));
            
            if (!addLoginResult.Succeeded)
            {
                string err = string.Join(", " , addLoginResult.Errors.Select(t => t).ToArray());
                logger.Error("userManager.AddLoginAsync" + err);
                
                return ServiceResult.Failed(new ServiceError() { Code = 1107 });
            }

            return ServiceResult.Success;
            //

            /*
            // Get the information about the user from the external login provider
            var info = await GetExternalLoginInfoAsync();
            if (info == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1001 });
            }

            var user = await userManager.FindByEmailAsync(activateAccountExtVM.Email);
            if (user == null)
            {
                user = new BIOME.Models.ApplicationUser
                {
                    UserName = activateAccountExtVM.Email,
                    Email = activateAccountExtVM.Email,
                    FirstName = activateAccountExtVM.FirstName,
                    LastName = activateAccountExtVM.LastName,
                    IsActive = true,
                    EmailConfirmed = true,
                    DateActivate = DateTimeOffset.Now
                };
                user.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);

                var createResult = await userManager.CreateAsync(user);
                if (!createResult.Succeeded)
                {
                    return GenerateUserViewUseableErrors(user.Email, user.Email, createResult.Errors);
                }

                var addPassword = await userManager.AddPasswordAsync(user.Id, activateAccountExtVM.Password);
                var addRoleResult = await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            }

            var addLoginResult = await userManager.AddLoginAsync(user.Id, info.Login);
            if (!addLoginResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1107 });
            }*/

           
        }

        

        public async Task<ServiceResult> ADRegistrationOrMappingAsync(AccountViewModel.ActivateAccountExtViewModel activateAccountExtVM, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink)
        {
            var user = await userManager.FindByEmailAsync(activateAccountExtVM.Email);
            if (user == null)
            {
                user = new BIOME.Models.ApplicationUser
                {
                    UserName = activateAccountExtVM.Email,
                    Email = activateAccountExtVM.Email,
                    //FirstName = activateAccountExtVM.FirstName,
                    //LastName = activateAccountExtVM.LastName,
                    PersonName = activateAccountExtVM.FirstName + " " + activateAccountExtVM.LastName,
                    IsActive = true,
                    AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE,
                    EmailConfirmed = true,
                    DateActivate = DateTimeOffset.Now
                };
                user.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.NParks);


                DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
                DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
                user.IV = DynamicPasswordCipher.IV;
                user.Key = DynamicPasswordCipher.Key;

                var createResult = await userManager.CreateAsync(user);
                if (!createResult.Succeeded)
                {
                    return GenerateUserViewUseableErrors(user.Email, user.Email, createResult.Errors);
                }

                var addPassword = await userManager.AddPasswordAsync(user.Id, activateAccountExtVM.Password);
                var addRoleResult = await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            }

            var addLoginResult = await userManager.AddLoginAsync(user.Id, new UserLoginInfo("AD", activateAccountExtVM.ExternalUserId));
            if (!addLoginResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1107 });
            }

            int platform_id = tokenService.CheckCurrentLogin(user.Id);

            await signInManager.SignInAsync(user, isPersistent: true, rememberBrowser: false);
            user.SessionID = HttpContext.Current.Session.SessionID;
            var updateResult = await userManager.UpdateAsync(user);
            tokenService.AddConCurrentAlertMessage(user.Id, platform_id, TokenService.PLATFORM_ID_BIOME,contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(user.Id);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ADFSRegistrationOrMappingAsync(AccountViewModel.ActivateAccountExtViewModel activateAccountExtVM, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink)
        {
            if (userManager.FindByName(activateAccountExtVM.Email) != null) {
                return ServiceResult.Failed(new ServiceError() { Code = 1006, Parameters = new List<string>() { activateAccountExtVM.Email } });

            }
            
            var user = await userManager.FindByEmailAsync(activateAccountExtVM.Email);
            if (user == null)
            {
                user = new BIOME.Models.ApplicationUser
                {
                    UserName = activateAccountExtVM.Email,
                    Email = activateAccountExtVM.Email,
                    //FirstName = activateAccountExtVM.FirstName,
                    //LastName = activateAccountExtVM.LastName,
                    PersonName = activateAccountExtVM.FirstName.Trim() + " " + activateAccountExtVM.LastName.Trim(),
                    IsActive = true,
                    AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE,
                    EmailConfirmed = true,
                    DateActivate = DateTimeOffset.Now
                };
                user.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.NParks);


                DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
                DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
                user.IV = DynamicPasswordCipher.IV;
                user.Key = DynamicPasswordCipher.Key;

                var createResult = await userManager.CreateAsync(user);
                if (!createResult.Succeeded)
                {
                    return GenerateUserViewUseableErrors(user.Email, user.Email, createResult.Errors);
                }

                await userManager.AddPasswordAsync(user.Id, activateAccountExtVM.Password);
                await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            }

            
            if (!await UserAssignedWithADUsername(user.Id, activateAccountExtVM.ExternalUserId))
            {
                await userManager.AddLoginAsync(user.Id, new UserLoginInfo("AD", activateAccountExtVM.ExternalUserId));

            }

            int platform_id = tokenService.CheckCurrentLogin(user.Id);
            await signInManager.SignInAsync(user, isPersistent: true, rememberBrowser: false);
            user.SessionID = HttpContext.Current.Session.SessionID;
            var updateResult = await userManager.UpdateAsync(user);

            tokenService.AddConCurrentAlertMessage(user.Id, platform_id, TokenService.PLATFORM_ID_BIOME,contactUsLink);
            //Note. BIOME auth ticke will be removed once user refreshed page, so no need to kill here.
            //clear all SgBioAtlas tokens to prevent concurrent login
            tokenService.KillMobileTokens(user.Id);

            return ServiceResult.Success;
        }

        public async Task<bool> UserAssignedWithADUsername(long userId, string username)
        {
            var logins = await userManager.GetLoginsAsync(userId);

            return logins.Any(l => l.LoginProvider == "AD" && l.ProviderKey == username);
        }

        public async Task<ServiceResult> AddADToUser(long userId, string username)
        {
            var user = await GetUserByIdAsync(userId);
            var result = await userManager.AddLoginAsync(userId, new UserLoginInfo("AD", username));
            if (!result.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, result.Errors);
            }

            return ServiceResult.Success;
        }

        
        //public async Task<ServiceResult> ADRegistrationOrMapping

        public void Logout()
        {
            long userId = 0;
            try
            {
                userId = HttpContext.Current.User.Identity.GetUserId<long>();
            }
            catch
            {
                userId = 0;
            }
            string ip = GetIP(true);
            if (userId > 0) auditTrailService.LogAuditTrail(userId, ip, (int)BIOME.Enumerations.Audit.Action.Logout, "User", "Logout.");

#if INTERNET
                        authenticationManager.SignOut(DefaultAuthenticationTypes.ApplicationCookie, DefaultAuthenticationTypes.ExternalCookie);
#elif INTRANET
            string callbackUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority) + HttpContext.Current.Response.ApplyAppPathModifier("~/Account/adfs_l?lout=1");
            //authenticationManager.SignOut(new AuthenticationProperties { RedirectUri = callbackUrl }, DefaultAuthenticationTypes.ApplicationCookie, DefaultAuthenticationTypes.ExternalCookie);
            var user = GetUserById(userId);
            if (user != null)
            {
                if (user.LastLoginType == Account.LoginType.LoginType_ADFS)
                {
                    //string callbackUrl = HttpContext.Current.Request.Url.GetLeftPart(UriPartial.Authority) + HttpContext.Current.Response.ApplyAppPathModifier("~/Account/adfs_l?lout=1");
                    authenticationManager.SignOut(new AuthenticationProperties { RedirectUri = callbackUrl }, DefaultAuthenticationTypes.ApplicationCookie, DefaultAuthenticationTypes.ExternalCookie);
                }
                else
                {

                    authenticationManager.SignOut(DefaultAuthenticationTypes.ApplicationCookie, DefaultAuthenticationTypes.ExternalCookie);
                }
            }

#endif


        }
        public void LogoutSession()
        {
            long userId = HttpContext.Current.User.Identity.GetUserId<long>();
            authenticationManager.SignOut(DefaultAuthenticationTypes.ApplicationCookie, DefaultAuthenticationTypes.ExternalCookie);
        }

        public async Task<ServiceResult> RegisterUserAsync(AccountViewModel.SignUpViewModel signupVM)
        {
            var newUser = Mapper.Map<ApplicationUser>(signupVM);
            newUser.PersonName = newUser.FirstName.Trim() + " " + newUser.LastName.Trim();
            string[] words = newUser.Email.Split('@'); //Phase4
            string domain = words[1];
            var group = groupService.GetGroupByDomain(domain);
            if (group != null)
            {
                newUser.Group = group;
            }
            else
            {
                newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            }
            /**
            DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
            DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);

            Removed IV and key to switch to hashing
            
            **/
            // newUser.IV = DynamicPasswordCipher.IV;
            // newUser.Key = DynamicPasswordCipher.Key;

            var signupResult = await userManager.CreateAsync(newUser);
            if (!signupResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(signupVM.Email, signupVM.Email, signupResult.Errors);
            }

            newUser = await userManager.FindByEmailAsync(signupVM.Email);
            if (newUser == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { signupVM.Email } });
            }
            
            var addPasswordResult = await userManager.AddPasswordAsync(newUser.Id, signupVM.Password);
            var addRoleResult = await userManager.AddToRoleAsync(newUser.Id, UserRoles.Public);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> RegisterUserAsync(AccountViewModel.ActivateAccountExtViewModel signupExtVM)
        {
            var newUser = Mapper.Map<ApplicationUser>(signupExtVM);
            newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
            DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
            newUser.IV = DynamicPasswordCipher.IV;
            newUser.Key = DynamicPasswordCipher.Key;
            var signupResult = await userManager.CreateAsync(newUser);
            if (!signupResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(signupExtVM.Email, signupExtVM.Email, signupResult.Errors);
            }

            newUser = await userManager.FindByEmailAsync(signupExtVM.Email);
            if (newUser == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { signupExtVM.Email } });
            }

            var addPasswordResult = await userManager.AddPasswordAsync(newUser.Id, signupExtVM.Password);
            var addRoleResult = await userManager.AddToRoleAsync(newUser.Id, UserRoles.Public);

            return ServiceResult.Success;
        }

        //public async Task<ServiceResult> RegisterUserAsync(ApiModelUser.SignUp signupVM, bool autoActivate = false)
        // public async Task<ServiceResult> RegisterUserAsync(ApiModelUser.SignUp signupVM)
        public async Task<ServiceResult> RegisterUserAsync(ApiModelUser.SignUp signupVM,string passwordInHMAC, string developerID)
        {
            var newUser = Mapper.Map<ApplicationUser>(signupVM);


            var checkHMAC = developerInfoService.ConstructDeveloperTokenHash($"{signupVM.Email}:{signupVM.Password}", developerID);
            if (checkHMAC != passwordInHMAC)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            /* if (autoActivate)
                {
                    newUser.EmailConfirmed = true;
                }*/

            //newUser.GeneratedPassword = true;

            //CR3&CR4 Phase1
            //newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            string[] words = newUser.Email.Split('@'); //Phase4
            string domain = words[1];
            var group = groupService.GetGroupByDomain(domain);
            if (group != null)
            {
                newUser.Group = group;
            }
            else
            {
                newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            }


            DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
            DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
            newUser.IV = DynamicPasswordCipher.IV;
            newUser.Key = DynamicPasswordCipher.Key;

            var createResult = await userManager.CreateAsync(newUser);
            if (!createResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(signupVM.Email, signupVM.Email, createResult.Errors);
            }

            newUser = await userManager.FindByEmailAsync(newUser.Email);

            /*string password = System.Web.Security.Membership.GeneratePassword(12, 4);
            if (!password.Any(x => char.IsDigit(x)))
            {
                Random rand = new Random();
                char[] pass = password.ToCharArray();
                pass[rand.Next(password.Length)] = Convert.ToChar(rand.Next(10) + '0');
                password = new string(pass);
            }
            var addResult = await userManager.AddPasswordAsync(newUser.Id, password);*/

            var addResult = await userManager.AddPasswordAsync(newUser.Id, signupVM.Password);
            var addRoleResult = await userManager.AddToRoleAsync(newUser.Id, UserRoles.Public);

            if (!addResult.Succeeded || !addRoleResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.Success;
            //return ServiceResult.SuccessWithOutput(password);
        }

        //old API
        //public async Task<ServiceResult> RegisterUserAsync(ApiModelUser.SignUp signupVM, bool autoActivate = false)
        //{
        //    var newUser = Mapper.Map<ApplicationUser>(signupVM);

        //    if (autoActivate)
        //    {
        //        newUser.EmailConfirmed = true;
        //    }

        //    newUser.GeneratedPassword = true;
        //    newUser.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
        //    DynamicPasswordCipher.IV = Utilities.Helpers.StringHelper.RandomString(32);
        //    DynamicPasswordCipher.Key = Utilities.Helpers.StringHelper.RandomString(64);
        //    newUser.IV = DynamicPasswordCipher.IV;
        //    newUser.Key = DynamicPasswordCipher.Key;

        //    var createResult = await userManager.CreateAsync(newUser);
        //    if (!createResult.Succeeded)
        //    {
        //        return GenerateUserViewUseableErrors(signupVM.Email, signupVM.Email, createResult.Errors);
        //    }

        //    newUser = await userManager.FindByEmailAsync(newUser.Email);
        //    string password = System.Web.Security.Membership.GeneratePassword(12, 4);
        //    if (!password.Any(x => char.IsDigit(x)))
        //    {
        //        Random rand = new Random();
        //        char[] pass = password.ToCharArray();
        //        pass[rand.Next(password.Length)] = Convert.ToChar(rand.Next(10) + '0');
        //        password = new string(pass);
        //    }
        //    var addResult = await userManager.AddPasswordAsync(newUser.Id, password);
        //    var addRoleResult = await userManager.AddToRoleAsync(newUser.Id, UserRoles.Public);

        //    if (!addResult.Succeeded || !addRoleResult.Succeeded)
        //    {
        //        return ServiceResult.Failed(new ServiceError() { Code = 1000 });
        //    }

        //    return ServiceResult.SuccessWithOutput(password);
        //}

        public async Task<bool> HasAccountExpiredAsync(long userId)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            return !user.IsActive;
        }

        public async Task<bool> HasPasswordExpiredAsync(long userId)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            /*if (user.Group == null) remove it to fix NPARKS-77
           {
               return false;
           }*/
            if (user.Group!=null && user.Group.Name.ToLower() == BIOME.Constants.Account.UserGroup.Public.ToLower())
            {
                return false;
            }

#if DEBUG && UAT
            //Never expire for local debugging
            return false;
#elif STAGING
                if ((user.DateLastChangePassword == default(DateTimeOffset)))
            {
                return (user.CreatedAt.AddDays(systemParameterService.GetPasswordExpiryPeriod()) < DateTimeOffset.Now);
            }
            else
            {
                return (user.DateLastChangePassword.AddDays((systemParameterService.GetPasswordExpiryPeriod())) < DateTimeOffset.Now);
            }
#else
            if ((user.DateLastChangePassword == default(DateTimeOffset)))
            {
                return (user.CreatedAt.AddDays(systemParameterService.GetPasswordExpiryPeriod()) < DateTimeOffset.Now);
            }
            else
            {
                return (user.DateLastChangePassword.AddDays((systemParameterService.GetPasswordExpiryPeriod())) < DateTimeOffset.Now);
            }
#endif
        }

        public async Task<bool> IsAccountLockedOutAsync(long id)
        {
            return await userManager.IsLockedOutAsync(id);
        }

        public async Task<ServiceResult> SetAccountUnlock(long id)
        {
            var setResult = await userManager.SetLockoutEndDateAsync(id, DateTimeOffset.Now.AddSeconds(-5));
            if (!setResult.Succeeded)
            {
                var user = await userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { id.ToString() } });
                }
                return GenerateUserViewUseableErrors(user.Email, user.Email, setResult.Errors);
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> SendForgetPasswordEmailAsync(string email, Uri resetPasswordUrl, Uri contactUsLink)
        {
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = "Email", Parameters = new List<string>() { email } });
            }

            string token = await userManager.GeneratePasswordResetTokenAsync(user.Id);
            string resetPasswordUrlString = resetPasswordUrl.ExtendQuery(new { code = token }).PathAndQuery;
            string contactUsUrlString = contactUsLink.PathAndQuery;

            //tmp added to fix gmail issues
            /*if (user.Email.ToLower().Contains("@gmail.com"))
            {
                emailService.SendResetPassword(user.FullName, "<EMAIL>", resetPasswordUrlString, contactUsUrlString);
            }*/

            emailService.SendResetPassword(user.PersonName, user.Email, resetPasswordUrlString, contactUsUrlString);

            return ServiceResult.SuccessWithOutput(token);
        }

        public async Task<ServiceResult> SendActivateAccountEmailAsync(string email, Uri activateAccountLink, Uri contactUsLink, bool externalLogin = false)
        {
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = "Email", Parameters = new List<string>() { email } });
                
            }
            
            string token = await userManager.GenerateEmailConfirmationTokenAsync(user.Id);
            var queries = new Dictionary<string, string>()
            {
                { "code", token }
            };
            if (externalLogin)
            {
                queries.Add("logintype", Account.LoginType.External);
            }

            string activateAccountUrlString = activateAccountLink.ExtendQuery(queries).PathAndQuery;
            string contactUsUrlString = contactUsLink.PathAndQuery;


            /*if (user.GeneratedPassword)
            {
                //user.GeneratedPassword is always false. Not allow to auto generate temp password.
                *//*var passwordHasher = new BIOMEPasswordHasher();
                var password = passwordHasher.DehashPassword(user.PasswordHash);
                emailService.SendActivateAccountWithTempPassword(user.FullName, user.Email, password, activateAccountUrlString, contactUsUrlString);*//*
            }
            else
            {
                //tmp added to fix gmail issues
                if (user.Email.ToLower().Contains("@gmail.com"))
                {
                    emailService.SendActivateAccount(user.FullName, "<EMAIL>", activateAccountUrlString, contactUsUrlString);
                }


                emailService.SendActivateAccount(user.FullName, user.Email, activateAccountUrlString, contactUsUrlString);
            }*/

            //tmp added to fix gmail issues
            /*if (user.Email.ToLower().Contains("@gmail.com"))
            {
                emailService.SendActivateAccount(user.FullName, "<EMAIL>", activateAccountUrlString, contactUsUrlString);
            }*/

            

            //send email regeardless of GeneratedPassword or not. Generated Password field was old features no longer used after sept 2020.
            emailService.SendActivateAccount(user.PersonName, user.Email, activateAccountUrlString, contactUsUrlString);


            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ResendActivationAsync(string email, Uri activateAccountUrl, Uri contactUsUrl)
        {
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { email } });
                
            }

            if (user.EmailConfirmed)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1030 });
            }


            var sendResult = await SendActivateAccountEmailAsync(email, activateAccountUrl, contactUsUrl);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> SendReactivateAccountEmailAsync(string email, Uri reactivateAccountUrl, Uri contactUsUrl, bool externalLogin = false)
        {
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = "Email", Parameters = new List<string>() { email } });
            }

            string token = await userManager.GenerateEmailConfirmationTokenAsync(user.Id);
            var queries = new Dictionary<string, string>()
            {
                { "code", token }
            };
            if (externalLogin)
            {
                queries.Add("logintype", Account.LoginType.External);
            }

            var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
            string reactivateAccountUrlString = reactivateAccountUrl.ExtendQuery(queries).PathAndQuery;
            emailService.SendAccountExpiry(user.PersonName, user.Email, accountExpireDate.ToString(), reactivateAccountUrlString, contactUsUrl.PathAndQuery);

            return ServiceResult.Success;
        }
        public string RetrieveTokenExpireDate(string Code) {

            try
            {
                var unprotectedData = userManager.Protector.Unprotect(Convert.FromBase64String(Code));
                var ms = new MemoryStream(unprotectedData);
                using (BinaryReader reader = new BinaryReader(ms))
                {
                    //var creationTime = new DateTimeOffset(reader.ReadInt64(), TimeSpan.Zero);
                    var creationTime = new DateTimeOffset(reader.ReadInt64(), TimeSpan.Zero);
                    var expirationTime = creationTime.AddMinutes(Constants.Account.Identity_TokenLifeSpanInMinutes);

                    return expirationTime.ToLocalTime().ToddMMyyyyhhmmsstt();

                    //var timezone = TimeZoneInfo.GetSystemTimeZones().FirstOrDefault(stz => stz.Id.Contains("Singapore Standard Time"));
                    //if (timezone == null)
                    //{
                    //    timezone = TimeZoneInfo.CreateCustomTimeZone("Singapore Standard Time", TimeSpan.FromHours(8), "(UTC+08:00) Kuala Lumpur, Singapore", "Singapore Standard Time");
                    //}
                    //DateTime date1 = TimeZoneInfo.ConvertTimeFromUtc(creationTime.UtcDateTime, timezone);

                    //DateTime dt2 = creationTime.UtcDateTime;
                    //return creationTime.DateTime.ToString();

                    //var expirationTime = creationTime + ApplicationUserManager.TokenLifespan;
                    //if (expirationTime < DateTimeOffset.UtcNow)
                    //{
                    //    tokenExpired = true;
                    //}
                }
            }
            catch {
                return "";
            }
        }



        public async Task<ServiceResult> ResetPasswordAsync(AccountViewModel.ResetPasswordViewModel resetPasswordVM, Uri passwordResetLink, Uri contactUsLink, Uri accountResetLink)
        {
            var user = await userManager.FindByEmailAsync(resetPasswordVM.Email);
            string userGroupName = "";
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = nameof(resetPasswordVM.Email), Parameters = new List<string>() { resetPasswordVM.Email } });
            }
            if (user.Group != null)
            {
                userGroupName = user.Group.Name;

            }
            var resetResult = await userManager.ResetPasswordAsync(user.Id, resetPasswordVM.Code, resetPasswordVM.Password);
            if (!resetResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, resetResult.Errors);
            }

            // When user reset password, update the last login date and reset the Account Expiry notification
            /*var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email))
                                                .Where(t => t.Task == BackgroundJobTask.TaskType.PasswordExpired || t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification || 
                                                        t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();*/

            var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email))
                                                .Where(t => t.Task == BackgroundJobTask.TaskType.PasswordExpired || t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification).ToList();
            foreach (var task in allAccountExpiredJobTasks)
            {
                if (task.Task == BackgroundJobTask.TaskType.PasswordExpired)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
                else if (task.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredNotificationFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
                //else if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
                //{
                //    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
                //    if (parameters.Recipient == user.Email)
                //    {
                //        backgroundJobService.DeleteScheduledJob(task.JobId);
                //    }
                //}
                //else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
                //{
                //    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
                //    if (parameters.Recipient == user.Email)
                //    {
                //        backgroundJobService.DeleteScheduledJob(task.JobId);
                //    }
                //}
            }
             
            if (user.Group != null)
            {
                userGroupName = user.Group.Name.ToLower();
            }
            if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            {
                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(systemParameterService.GetPasswordExpiryPeriod());
                //var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
                backgroundJobService.SchedulePasswordExpired(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate
                    });

                backgroundJobService.SchedulePasswordExpiredNotification(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate.AddDays(-14)
                    });

                //backgroundJobService.ScheduleAccountExpired(
                //    user.FullName,
                //    user.Email,
                //    accountExpireDate,
                //    accountResetLink,
                //    contactUsLink,
                //    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                //    {
                //        LaterDate = accountExpireDate
                //    });

                //backgroundJobService.ScheduleAccountExpiredNotification(
                //    user.FullName,
                //    user.Email,
                //    accountExpireDate,
                //    accountResetLink,
                //    contactUsLink,
                //    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                //    {
                //        LaterDate = accountExpireDate.AddDays(-14)
                //    });
            }

            string contactUsUrlString = contactUsLink.PathAndQuery;
            emailService.SendPasswordResetSuccess(user.PersonName, user.Email, contactUsUrlString);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ChangePasswordAsync(AccountViewModel.ChangePasswordViewModel changePasswordVM, Uri passwordResetLink, Uri contactUsLink)
        {
            var user = await userManager.FindByIdAsync(changePasswordVM.UserId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, Parameters = new List<string>() { changePasswordVM.UserId.ToString() } });
            }
            var changeResult = await userManager.ChangePasswordAsync(changePasswordVM.UserId, changePasswordVM.ExistingPassword, changePasswordVM.Password);
            if (!changeResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, changeResult.Errors);
            }

            user.GeneratedPassword = false;
            await userManager.UpdateAsync(user);

            var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.PasswordExpired || t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification).ToList();
            foreach (var task in allAccountExpiredJobTasks)
            {
                if (task.Task == BackgroundJobTask.TaskType.PasswordExpired)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
                else if (task.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredNotificationFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
            }

            string userGroupName = "";
            if (user.Group != null)
            {
                userGroupName = user.Group.Name.ToLower();
            }
            if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            {
                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(systemParameterService.GetPasswordExpiryPeriod());
                backgroundJobService.SchedulePasswordExpired(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate
                    });
                backgroundJobService.SchedulePasswordExpiredNotification(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate.AddDays(-14)
                    });
            }
            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ChangePasswordAsyncAPI(ApiModelUser.ChangePasswordModelAPI changePasswordVM, Uri passwordResetLink, Uri contactUsLink)
        {
            var checkHMAC_OldPwd = developerInfoService.ConstructDeveloperTokenHash($"{changePasswordVM.Email}:{changePasswordVM.OldPassword}", changePasswordVM.DeveloperID);
            if (checkHMAC_OldPwd != changePasswordVM.OldPasswordInHMAC)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }


            var checkHMAC_NewPwd = developerInfoService.ConstructDeveloperTokenHash($"{changePasswordVM.Email}:{changePasswordVM.NewPassword}", changePasswordVM.DeveloperID);
            if (checkHMAC_NewPwd != changePasswordVM.NewPasswordInHMAC)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

        
            var user = await userManager.FindAsync(changePasswordVM.Email, changePasswordVM.OldPassword);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101 });
            }


           var changeResult = await userManager.ChangePasswordAsync(changePasswordVM.UserId, changePasswordVM.OldPassword, changePasswordVM.NewPassword);
            if (!changeResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, changeResult.Errors);
            }

            user.GeneratedPassword = false;
            await userManager.UpdateAsync(user);

            var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.PasswordExpired || t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification).ToList();
            foreach (var task in allAccountExpiredJobTasks)
            {
                if (task.Task == BackgroundJobTask.TaskType.PasswordExpired)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
                else if (task.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification)
                {
                    var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplatePasswordExpiredNotificationFields>(task.Parameters);
                    if (parameters.Recipient == user.Email)
                    {
                        backgroundJobService.DeleteScheduledJob(task.JobId);
                    }
                }
            }

            string userGroupName = "";
            if (user.Group != null)
            {
                userGroupName = user.Group.Name.ToLower();
            }
            if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            {
                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(systemParameterService.GetPasswordExpiryPeriod());
                backgroundJobService.SchedulePasswordExpired(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate
                    });
                backgroundJobService.SchedulePasswordExpiredNotification(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate.AddDays(-14)
                    });
            }

            return ServiceResult.Success;
        }
        private Group getGroupForUser(string email) {
            string[] words = email.Split('@'); //Phase4
            string domain = words[1];
            var group = groupService.GetGroupByDomain(domain);
            if (group != null)
            {
                return group;
            }
            else
            {
                return groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            }
        }
        public async Task<ServiceResult> ReactivateAccountLoggedInAsync(long userId, Uri passwordResetLink, Uri accountResetLink, Uri loginLink, Uri contactUsLink)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }

            user.IsActive = true;
            user.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
            user.LastSuspendDate = DateTime.MinValue.ToUniversalTime();
            user.LastDeActivateDate = DateTime.MinValue.ToUniversalTime();
            user.DateActivate = DateTimeOffset.Now;
            user.ForcedInactiveByAdmin = false;

            //Reasign group based on email domain if current group is public
            if (user.Group!=null && user.Group.Name.Equals(BIOME.Constants.Account.UserGroup.Public,StringComparison.OrdinalIgnoreCase)) 
            {
                user.Group = getGroupForUser(user.Email);
            }

            //Reasign the public role
            bool hasPubicRole = user.Roles.Any(r => r.RoleId.Equals(UserRoles.PublicId));
            if (!hasPubicRole) {
                await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            }

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1103, Parameters = new List<string>() { userId.ToString() } });
            }
            

            emailService.SendAccountResetSuccess(user.PersonName, user.Email, loginLink.PathAndQuery, contactUsLink.PathAndQuery);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ReactivateAccountToken(string email, string code, Uri passwordResetLink, Uri accountResetLink, Uri loginLink, Uri contactUsLink)
        {
            var user = await userManager.FindByEmailAsync(email);
            if (user == null)
            {
                //return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { user.Id.ToString() } });
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() {email } });
            }

            user.IsActive = true;
            user.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
            user.LastSuspendDate = DateTime.MinValue.ToUniversalTime();
            user.LastDeActivateDate = DateTime.MinValue.ToUniversalTime();
            user.DateActivate = DateTimeOffset.Now;
            user.ForcedInactiveByAdmin = false;
            var confirmEmailResult = await userManager.ConfirmEmailAsync(user.Id, code);
            if (!confirmEmailResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1104 });
            }
            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1103, Parameters = new List<string>() { user.Id.ToString() } });
            }
            
            emailService.SendAccountResetSuccess(user.PersonName, user.Email, loginLink.PathAndQuery, contactUsLink.PathAndQuery);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ActivateAccountAsync(AccountViewModel.ActivateAccountViewModel activateAccountVM, Uri passwordResetLink, Uri accountResetLink, Uri contactUsLink)
        {
            var user = await userManager.FindByEmailAsync(activateAccountVM.Email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = nameof(activateAccountVM.Email), Parameters = new List<string>() { activateAccountVM.Email } });
            }

            user.EmailConfirmed = true;
            user.IsActive = true;
            user.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
            user.LastSuspendDate = DateTime.MinValue.ToUniversalTime();
            user.LastDeActivateDate = DateTime.MinValue.ToUniversalTime();
            user.DateActivate = DateTimeOffset.Now;
            user.ForcedInactiveByAdmin = false;
            var confirmEmailResult = await userManager.ConfirmEmailAsync(user.Id, activateAccountVM.Code);
            if (!confirmEmailResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1104 });
            }
            string userGroupName = "";
            if (user.Group != null)
            {
                userGroupName = user.Group.Name.ToLower();
            }
            if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            {
                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(systemParameterService.GetPasswordExpiryPeriod());
                var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
                backgroundJobService.SchedulePasswordExpired(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate
                    });
                backgroundJobService.SchedulePasswordExpiredNotification(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    passwordResetLink,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate.AddDays(-14)
                    });

                //backgroundJobService.ScheduleAccountExpired(
                //    user.FullName,
                //    user.Email,
                //    accountExpireDate,
                //    accountResetLink,
                //    contactUsLink,
                //    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                //    {
                //        LaterDate = accountExpireDate
                //    });

                //backgroundJobService.ScheduleAccountExpiredNotification(
                //    user.FullName,
                //    user.Email,
                //    accountExpireDate,
                //    accountResetLink,
                //    contactUsLink,
                //    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                //    {
                //        LaterDate = accountExpireDate.AddDays(-14)
                //    });
            }
            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ReactivateAccountAsync(AccountViewModel.ReactivateAccountViewModel reactivateAccountVM, Uri loginLink, Uri accountResetLink, Uri contactUsLink)
        {
            var user = await userManager.FindByEmailAsync(reactivateAccountVM.Email);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1004, ModelPropertyName = nameof(reactivateAccountVM.Email), Parameters = new List<string>() { reactivateAccountVM.Email } });
            }

            var tokenValid = await userManager.VerifyUserTokenAsync(user.Id, Account.TokenPurpose.ReactivateAccount, reactivateAccountVM.Code);
            if (!tokenValid)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1102 });
            }

            user.EmailConfirmed = true;
            user.IsActive = true;
            user.DateActivate = DateTimeOffset.Now;
            user.ForcedInactiveByAdmin = false;
            user.AccountStatus = BIOME.Constants.Account.AccountStatus.ACTIVE;
            user.LastSuspendDate = DateTime.MinValue.ToUniversalTime();
            user.LastDeActivateDate = DateTime.MinValue.ToUniversalTime();

            //Reasign group based on email domain if current group is public
            if (user.Group != null && user.Group.Name.Equals(BIOME.Constants.Account.UserGroup.Public, StringComparison.OrdinalIgnoreCase))
            {
                user.Group = getGroupForUser(user.Email);
            }

            //Reasign the public role
            bool hasPubicRole = user.Roles.Any(r => r.RoleId.Equals(UserRoles.PublicId));
            if (!hasPubicRole)
            {
                await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            }

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1103, Parameters = new List<string>() { reactivateAccountVM.Email } });
            }

            //var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Parameters.Contains(user.Email)).Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
            //foreach (var task in allAccountExpiredJobTasks)
            //{
            //    if (task.Task == BackgroundJobTask.TaskType.AccountExpired)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //    else if (task.Task == BackgroundJobTask.TaskType.AccountExpiredNotification)
            //    {
            //        var parameters = JsonConvert.DeserializeObject<EmailTemplateViewModel.EmailTemplateAccountExpiredNotificationFields>(task.Parameters);
            //        if (parameters.Recipient == user.Email)
            //        {
            //            backgroundJobService.DeleteScheduledJob(task.JobId);
            //        }
            //    }
            //}
            //string userGroupName = "";
            //if (user.Group != null)
            //{
            //    userGroupName = user.Group.Name.ToLower();
            //}
            //if (userGroupName != BIOME.Constants.Account.UserGroup.Public.ToLower())
            //{
            //    var accountExpireDate = user.DateLastLoginOrActivate.AddDays(systemParameterService.GetAccountExpiryPeriod());
            //    backgroundJobService.ScheduleAccountExpired(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate
            //        });
            //    backgroundJobService.ScheduleAccountExpiredNotification(
            //        user.FullName,
            //        user.Email,
            //        accountExpireDate,
            //        accountResetLink,
            //        contactUsLink,
            //        new BackgroundJobService.BackgroundJobScheduleLaterDate()
            //        {
            //            LaterDate = accountExpireDate.AddDays(-14)
            //        });
            //}
            emailService.SendAccountResetSuccess(user.PersonName, user.Email, loginLink.PathAndQuery, contactUsLink.PathAndQuery);

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> SendAccountLoginOTPAsync(long userId, string optCode, string optExpiryDate, Uri contactUsUrl)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }
 
            emailService.SendAccountLoginOTP(user.PersonName, user.Email, optCode, optExpiryDate, contactUsUrl.PathAndQuery);

            return ServiceResult.Success;
        }

        public ServiceResult GetUserProfile(long id, out AccountViewModel.ProfileViewModel profileVM)
        {
            profileVM = null;
            var user = userManager.FindById(id);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { id.ToString() } });
            }

            ConfigurationService.Instance.AccountExpiryPeriod = systemParameterService.GetAccountExpiryPeriod();

            profileVM = Mapper.Map<AccountViewModel.ProfileViewModel>(user);
            var groupsListing = groupService.GetGroupsHierarchyForGroup(user.Group);
            profileVM.UserGroupTree = groupsListing.ToList();

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> EditProfileAsync(AccountViewModel.ProfileEditViewModel editVM)
        {
            var user = await userManager.FindByIdAsync(editVM.Id);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { editVM.Id.ToString() } });
            }

            Mapper.Map(editVM, user);

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, updateResult.Errors);
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> UpdateProfileImgAsync(string filename, string headerContentFullPath, AccountViewModel.ProfileImageEditViewModel editVM)
        {
            var user = await userManager.FindByIdAsync(editVM.Id);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { editVM.Id.ToString() } });
            }

            var tempOldFilename = string.Empty;
            if (!string.IsNullOrEmpty(user.ProfilePicImgName))
            {
                tempOldFilename = user.ProfilePicImgName;
            }

            user.ProfilePicImgName = filename;

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.UserName, updateResult.Errors);
            }

            if (!string.IsNullOrEmpty(tempOldFilename))
            {
                //File.Delete(Path.Combine(headerContentFullPath, tempOldFilename));
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> UpdateUserAsync(UserViewModel.DetailViewModel updatedVM)
        {
            var existingUser = await GetUserByIdAsync(updatedVM.Id);
            bool isActiveToInactive = false;
            if (existingUser.IsActive && !updatedVM.IsActive)
            {
                isActiveToInactive = true;
                //logger.Error("Set User Inactive in UserService UpdateUserAsync " + existingUser.Id + " " + existingUser.FullName);

            }

            Mapper.Map(updatedVM, existingUser);


            var existingRoles = userManager.GetRoles(updatedVM.Id);

            var rolesToKeep = existingRoles.Intersect(updatedVM.RolesSelected);

            var rolesToRemove = existingRoles.Except(rolesToKeep);

            foreach (var role in rolesToRemove)
            {
                await userManager.RemoveFromRoleAsync(updatedVM.Id, role);
            }

            var rolesToAdd = updatedVM.RolesSelected.Except(rolesToKeep);

            foreach (var role in rolesToAdd)
            {
                await userManager.AddToRoleAsync(updatedVM.Id, role);
            }

            if (updatedVM.UserGroupChanged.Count > 0)
            {
                var groupsSelected = updatedVM.UserGroupChanged.Select(ug => groupService.GetGroupById(ug));
                existingUser.Group = groupService.GetHighestGroupFromList(groupsSelected);
                //existingUser.Group = null;
            }
            else
            {
                var existingGroup = existingUser.Group;

                if (existingGroup !=null)
                {
                    existingUser.Group = null;
                }


            }
            bool roleRemoved = false;

            if (updatedVM.RolesSelected.Count == 0)
            {
                //Update "Last Deactivate Date" whenever there is an update of user's role to "no role"
                existingUser.LastDeActivateDate = DateTime.Now;

                roleRemoved = true;
                
            }
            else
            {
                roleRemoved = false;
                existingUser.LastDeActivateDate = DateTime.MinValue.ToUniversalTime();
                
            }

            if (updatedVM.IsActive)
            {
                existingUser.LastSuspendDate = DateTime.MinValue.ToUniversalTime();

                if (roleRemoved)
                {
                    existingUser.AccountStatus = Account.AccountStatus.DEACTIVATED;
                }
                else
                {
                    existingUser.AccountStatus = Account.AccountStatus.ACTIVE;
                }
            }
            else
            {
                if (isActiveToInactive)
                {
                    //Update "Last suspend date" whenever there is an update of user's status from "active" to "inactive".
                    existingUser.LastSuspendDate = DateTime.Now;
                    isActiveToInactive = false;
                }

                if (roleRemoved)
                {
                    existingUser.AccountStatus = Account.AccountStatus.DEACTIVATED;
                }
                else
                {
                    existingUser.AccountStatus = Account.AccountStatus.SUSPENDED;
                }
            }







            var updateResult = await userManager.UpdateAsync(existingUser);
            if (!updateResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(existingUser.Email, existingUser.Email, updateResult.Errors);
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> UpdateUserRolePermitManagerAsync(UserViewModel.DetailViewModel updatedVM)
        {
            var existingRoles = userManager.GetRoles(updatedVM.Id);

            var rolesToKeep = existingRoles.Intersect(updatedVM.RolesSelected);

            var rolesToRemove = existingRoles.Except(rolesToKeep);

            foreach (var role in rolesToRemove)
            {
                if (string.Equals(role, UserRoles.PermitManager) || string.Equals(role, UserRoles.SiteManager))
                {
                    await userManager.RemoveFromRoleAsync(updatedVM.Id, role);
                }
            }

            var rolesToAdd = updatedVM.RolesSelected.Except(rolesToKeep);

            foreach (var role in rolesToAdd)
            {
                if (string.Equals(role, UserRoles.PermitManager) || string.Equals(role, UserRoles.SiteManager))
                {
                    await userManager.AddToRoleAsync(updatedVM.Id, role);
                }
            }

            
            if (updatedVM.RolesSelected.Count == 0)
            {
                //Update "Last Deactivate Date" whenever there is an update of user's role to "no role"
                var existingUser = await GetUserByIdAsync(updatedVM.Id);
                existingUser.LastDeActivateDate = DateTime.Now;
                await userManager.UpdateAsync(existingUser);
            }


            

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> CreateUserAsync(AccountViewModel.SignUpViewModel userVM, long modifiedByUserId)
        {
            var user = new ApplicationUser()
            {
                UserName = userVM.Email,
                Email = userVM.Email,
                //FirstName = userVM.FirstName,
                //LastName = userVM.LastName,
                PersonName = userVM.FirstName.Trim() + " " + userVM.LastName.Trim(),
                IsActive = true,
                AccountStatus = Account.AccountStatus.ACTIVE
        };
            user.Group = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.Public);
            var createResult = await userManager.CreateAsync(user, userVM.Password);
            if (!createResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(userVM.Email, userVM.Email, createResult.Errors);
            }
            var addRoleResult = await userManager.AddToRoleAsync(user.Id, UserRoles.Public);
            user = await userManager.FindByEmailAsync(user.Email);

            return ServiceResult.Success;
        }

        public async Task<bool> HasFollowedUserAsync(long userId, long followingId)
        {
            if (userId == followingId)
            {
                return false;
            }

            var user = await userManager.FindByIdAsync(userId);
            var following = await userManager.FindByIdAsync(followingId);

            return user.Following.Contains(following);
        }

        public async Task<ServiceResult> UnfollowUserAsync(long userId, long followingId)
        {
            if (userId == followingId)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1201 });
            }

            var user = await userManager.FindByIdAsync(userId);
            var following = await userManager.FindByIdAsync(followingId);

            if (user == null || following == null)
            {
                var parameters = new List<string>();
                if (user == null)
                {
                    parameters.Add(userId.ToString());
                }
                if (following == null)
                {
                    parameters.Add(followingId.ToString());
                }
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { String.Join(", ", parameters) } });
            }

            bool hasFollowed = user.Following.Contains(following);
            if (hasFollowed)
            {
                user.Following.Remove(following);
                var updateResult = await userManager.UpdateAsync(user);
                if (!updateResult.Succeeded)
                {
                    return GenerateUserViewUseableErrors(user.Email, user.Email, updateResult.Errors);
                }
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> FollowUserAsync(long userId, long followingId, Uri profileLink, Uri contactUsLink)
        {
            if (userId == followingId)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1201 });
            }

            var user = await userManager.FindByIdAsync(userId);
            var following = await userManager.FindByIdAsync(followingId);

            if (user == null || following == null)
            {
                var parameters = new List<string>();
                if (user == null)
                {
                    parameters.Add(userId.ToString());
                }
                if (following == null)
                {
                    parameters.Add(followingId.ToString());
                }
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { String.Join(", ", parameters) } });
            }

            bool hasFollowed = user.Following.Contains(following);
            if (!hasFollowed)
            {
                user.Following.Add(following);
                var updateResult = await userManager.UpdateAsync(user);
                if (!updateResult.Succeeded)
                {
                    return GenerateUserViewUseableErrors(user.Email, user.Email, updateResult.Errors);
                }
                emailService.SendNewFollower(following.PersonName, following.Email, following.Followers.Count, profileLink.PathAndQuery, contactUsLink.PathAndQuery);
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> FollowUnfollowUserAsync(long userId, long followingId, Uri profileLink, Uri contactUsLink)
        {
            if (userId == followingId)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1201 });
            }

            var user = await userManager.FindByIdAsync(userId);
            var following = await userManager.FindByIdAsync(followingId);

            if (user == null || following == null)
            {
                var parameters = new List<string>();
                if (user == null)
                {
                    parameters.Add(userId.ToString());
                }
                if (following == null)
                {
                    parameters.Add(followingId.ToString());
                }
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { String.Join(", ", parameters) } });
            }

            bool addFollower = false;
            bool hasFollowed = user.Following.Contains(following);
            if (hasFollowed)
            {
                user.Following.Remove(following);
            }
            else
            {
                user.Following.Add(following);
                addFollower = true;
            }

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return GenerateUserViewUseableErrors(user.Email, user.Email, updateResult.Errors);
            }

            if (addFollower)
            {
                emailService.SendNewFollower(following.PersonName, following.Email, following.Followers.Count, profileLink.PathAndQuery, contactUsLink.PathAndQuery);
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> SetUserBlacklistedStateAsync(long userId, bool isBlacklisted)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }

            user.Blacklisted = isBlacklisted;

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                //Temp error
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.Success;
        }



        public async Task<ServiceResult> AcceptPermitTermsAsync(long userId)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }

            user.HasAgreedPermitApplicationTnC = true;

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 3101 });
            }

            return ServiceResult.Success;
        }

        public async Task<ServiceResult> ResetPermitTermsAsync(long userId)
        {
            var user = await userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }

            user.HasAgreedPermitApplicationTnC = false;

            var updateResult = await userManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1000 });
            }

            return ServiceResult.Success;
        }

        public ServiceResult GetUserTerms(long userId, out PermitViewModel.TermsViewModel terms)
        {
            terms = null;

            var user = userManager.FindById(userId);
            if (user == null)
            {
                return ServiceResult.Failed(new ServiceError() { Code = 1101, Parameters = new List<string>() { userId.ToString() } });
            }
            terms = Mapper.Map<PermitViewModel.TermsViewModel>(user);

            return ServiceResult.Success;
        }

        public void RescheduleAllPasswordExpiredSendEmailJobs(Uri passwordResetLink, Uri contactUsLink)
        {
            int daysPasswordExpire = systemParameterService.GetPasswordExpiryPeriod();
            var allPasswordExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Task == BackgroundJobTask.TaskType.PasswordExpired || t.Task == BackgroundJobTask.TaskType.PasswordExpiredNotification).ToList();
            foreach (var task in allPasswordExpiredJobTasks)
            {
                backgroundJobService.DeleteScheduledJob(task.JobId);
            }

            var users = GetUsers().ToList().Where(u => u.IsActive && (u.Group!=null? u.Group.Name.ToLower():"") != BIOME.Constants.Account.UserGroup.Public.ToLower()).Select(u => new UserViewModel.UserScheduleViewModel { Email = u.Email, DateActivate = u.DateActivate, DateLastLogin = u.DateLastLogin, DateLastChangePassword = u.DateLastChangePassword, PersonName = u.PersonName });

            foreach (var user in users)
            {
                var constructedPasswordResetUri = passwordResetLink.ExtendQuery(new { email = user.Email });

                var passwordExpireDate = user.DateLastChangePasswordActual.AddDays(daysPasswordExpire);
                backgroundJobService.SchedulePasswordExpired(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    constructedPasswordResetUri,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate
                    });
                backgroundJobService.SchedulePasswordExpiredNotification(
                    user.PersonName,
                    user.Email,
                    passwordExpireDate,
                    constructedPasswordResetUri,
                    contactUsLink,
                    new BackgroundJobService.BackgroundJobScheduleLaterDate()
                    {
                        LaterDate = passwordExpireDate.AddDays(-14)
                    });
            }
        }

        //public void RescheduleAllAccountExpiredSendEmailJobs(Uri accountResetLink, Uri contactUsLink)
        //{
        //    int daysAccountExpire = systemParameterService.GetAccountExpiryPeriod();
        //    var allAccountExpiredJobTasks = dbContext.BackgroundJobTasks.Where(t => t.Task == BackgroundJobTask.TaskType.AccountExpired || t.Task == BackgroundJobTask.TaskType.AccountExpiredNotification).ToList();
        //    foreach (var task in allAccountExpiredJobTasks)
        //    {
        //        backgroundJobService.DeleteScheduledJob(task.JobId);
        //    }

        //    var users = GetUsers().ToList().Where(u => u.IsActive && (u.Group!=null?u.Group.Name.ToLower():"") != BIOME.Constants.Account.UserGroup.Public.ToLower()).Select(u => new UserViewModel.UserScheduleViewModel { Email = u.Email, DateActivate = u.DateActivate, DateLastLogin = u.DateLastLogin, DateLastChangePassword = u.DateLastChangePassword, FirstName = u.FirstName, LastName = u.LastName, PersonName = u.PersonName, group = u.Group != null ? u.Group.Name : "Public" });

        //    foreach (var user in users)
        //    {
        //        var constructedAccountResetUri = accountResetLink.ExtendQuery(new { email = user.Email });

        //        var passwordExpireDate = user.DateLastLoginOrActivate.AddDays(daysAccountExpire);
        //        backgroundJobService.ScheduleAccountExpired(
        //            user.FullName,
        //            user.Email,
        //            passwordExpireDate,
        //            constructedAccountResetUri,
        //            contactUsLink,
        //            new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //            {
        //                LaterDate = passwordExpireDate
        //            });
        //        backgroundJobService.ScheduleAccountExpiredNotification(
        //            user.FullName,
        //            user.Email,
        //            passwordExpireDate,
        //            constructedAccountResetUri,
        //            contactUsLink,
        //            new BackgroundJobService.BackgroundJobScheduleLaterDate()
        //            {
        //                LaterDate = passwordExpireDate.AddDays(-14)
        //            });
        //    }
        //}

        public byte[] ExportUsersInfoForAudit()
        {

            //fix for NPARK/BIOME/NCODE/2020_0123
            //var activeUsers = GetUsers().ToList();
            var activeUsers = GetAllUsers();
            List<long> userBadgeList = badgesService.GetUserBadgeUserIDList();
            int passwordExpiry = systemParameterService.GetPasswordExpiryPeriod();
            int accountExpiry = systemParameterService.GetAccountExpiryPeriod();


            var list = new List<UserViewModel.UserExportCSVViewModel>();
            
            foreach (var activeUser in activeUsers)
            {
                
                
                var exportItem = new UserViewModel.UserExportCSVViewModel();
                exportItem.Id = activeUser.Id;
                //exportItem.FirstName = activeUser.FirstName;
                //exportItem.LastName = activeUser.LastName;
                exportItem.PersonName = activeUser.PersonName;
                exportItem.Email = activeUser.Email.Replace("\r\n","").Replace("\n","").Replace("\r","");
                exportItem.Group = activeUser.Group?.Name;
                exportItem.SystemAdmin = activeUser.Roles.Any(r=>r.RoleId.Equals(UserRoles.SystemAdminId));
                exportItem.PermitManager = activeUser.Roles.Any(r => r.RoleId.Equals(UserRoles.PermitManagerId));
                exportItem.SiteManager = activeUser.Roles.Any(r => r.RoleId.Equals(UserRoles.SiteManagerId));
                exportItem.Expert = activeUser.Roles.Any(r => r.RoleId.Equals(UserRoles.ExpertId));
                exportItem.ResourceUploader = activeUser.Roles.Any(r => r.RoleId.Equals(UserRoles.ResourceUploaderId));
                exportItem.Public = activeUser.Roles.Any(r => r.RoleId.Equals(UserRoles.PublicId));
                exportItem.LastLogin = activeUser.DateLastLogin.DateTime != DateTime.MinValue ? activeUser.DateLastLogin.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                exportItem.CreatedAt = activeUser.CreatedAt.ToString("dd/MM/yyyy HH:mm:ss");
                exportItem.Status = activeUser.IsActive ? "Active" : "Inactive";
                exportItem.IsBlackListed = activeUser.Blacklisted;
                exportItem.LockoutEndDate = activeUser.LockoutEndDateUtc != null ? activeUser.LockoutEndDateUtc.ToString() : "N/A";
                exportItem.LockoutEnabled = activeUser.LockoutEnabled;
                exportItem.AccessFailedCount = activeUser.AccessFailedCount.ToString();
                exportItem.DateActivated = activeUser.DateActivate.Year > 2000 ? activeUser.DateActivate.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                exportItem.IsSightingNoNotification = activeUser.IsSightingNoNotification;
                exportItem.LastSuspendDate = activeUser.LastSuspendDate.Year > 2000 ? activeUser.LastSuspendDate.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                exportItem.LastDeactivateDate = activeUser.LastDeActivateDate.Year > 2000 ? activeUser.LastDeActivateDate.ToString("dd/MM/yyyy HH:mm:ss") : "N/A";
                //exportItem.AccountStatus = activeUser.AccountStatus != null ? activeUser.AccountStatus : "ACTIVE";
                exportItem.AccountStatus = activeUser.AccountStatus;
                exportItem.PasswordExpiryDate = activeUser.DateLastChangePasswordActual.AddDays(passwordExpiry).ToString("dd/MM/yyyy HH:mm:ss");
                exportItem.AccountExpiryDate = activeUser.DateLastLoginOrActivate.AddDays(accountExpiry).ToString("dd/MM/yyyy HH:mm:ss");
                //exportItem.BadgeCount = badgesService.GetUserBadgeList(activeUser.Id).Count();
                //fix for NPARK/BIOME/NCODE/2020_0123
                exportItem.BadgeCount = userBadgeList.Count(b=>b==activeUser.Id);

                list.Add(exportItem);
            }

           // StringWriter sw = new StringWriter();
            //var csv = new CsvWriter(sw);
            using (var memoryStream = new MemoryStream())
            {
                using (var sw = new StreamWriter(memoryStream, new UTF8Encoding(true)))
                using (var csv = new CsvWriter(sw))
                {
                    csv.Configuration.RegisterClassMap<UserExportCSVMapper>();
                    csv.WriteHeader<UserViewModel.UserExportCSVViewModel>();
                    csv.WriteRecords(list);

                    sw.Flush();
                    //return GetBytes(sw.ToString());
                    return memoryStream.ToArray();
                }
            }

           
        }

        /*public byte[] ExportNparksUsersInfoForAudit()
        {
            //var export_users = GetUsers().ToList();
            //var export_users = GetAllUsers();


            Group nparkGroup = groupService.GetGroupByName(BIOME.Constants.Account.UserGroup.NParks);
            var allNparkGrups = groupService.GetAllChildrenGroups(nparkGroup);
            //var allNparkGrupNames = allNparkGrups.Select(t => t.Name).ToArray().Select(t => t.ToLower());
            var list = new List<UserViewModel.NparksUserExport_ACE_CSVViewModel>();
            int accountExpiryPeriod = systemParameterService.GetAccountExpiryPeriod();
            var allNparkGrupID = allNparkGrups.Select(t => t.Id).ToArray();
            var export_users = GetAllUsersByGroup(allNparkGrupID);
            foreach (var export_user in export_users)
            {
                //var roles = userManager.GetRoles(activeUser.Id);

                var exportItem = new UserViewModel.NparksUserExport_ACE_CSVViewModel();
                //var logins = userManager.GetLogins(export_user.Id);
                var groupname = "";
                if (export_user.Group != null)
                {
                    groupname = export_user.Group.Name.ToLower();

                }
                exportItem.USERID = export_user.UserName.Replace("|", "").Trim();
                if (export_user.Logins.Any())
                {
                    //exportItem.SOEID = logins.FirstOrDefault().ProviderKey;
                    exportItem.SOEID = export_user.Logins.FirstOrDefault().ProviderKey.Trim();
                }
                exportItem.STAFFNAME = string.IsNullOrEmpty(export_user.PersonName) ? "" : export_user.PersonName.Replace("|", "").Trim();
                exportItem.PERSONALNO = string.IsNullOrEmpty(export_user.PhoneNumber) ? "N/A" : export_user.PhoneNumber.Replace("|", "").Trim();
                exportItem.EMAIL = string.IsNullOrEmpty(export_user.Email) ? "" : export_user.Email.Replace("|", "").Trim();
                exportItem.VALIDITYDATE = export_user.DateLastLoginOrActivate.AddDays(accountExpiryPeriod).ToddMMyyyy();
                exportItem.CLUSTERNAME = "N/A";
                exportItem.DIVISIONNAME = "N/A";
                exportItem.BRANCHNAME = groupname.Replace("|", "").Trim();
                exportItem.DESIGNATION = "N/A";
                //exportItem.ACCSTATUS = (export_user.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE : ((export_user.AccountStatus == BIOME.Constants.Account.AccountStatus.DEACTIVATED ? export_user.AccountStatus : BIOME.Constants.Account.AccountStatus.SUSPENDED)));
                exportItem.ACCSTATUS = export_user.AccountStatus;


                //exportItem.ROLECODE = exportItem.ROLEDESC = String.Join("/", GetRolesForUser(export_user.Id).Select(t => t.ToProperRoleName()).ToList());// GetRolesForUser(activeUser.Id).ToList();
                if (export_user.Roles != null && export_user.Roles.Any())
                {
                    exportItem.ROLECODE = exportItem.ROLEDESC = String.Join("/", export_user.Roles.Select(t => t.RoleId).Select(t => t.ToProperRoleName()).ToList());
                }
                exportItem.ROLECODE = string.IsNullOrEmpty(exportItem.ROLECODE) ? "" : exportItem.ROLECODE.Replace("|", "").Trim();
                exportItem.ROLEDESC = string.IsNullOrEmpty(exportItem.ROLEDESC) ? "" : exportItem.ROLEDESC.Replace("|", "").Trim();
                exportItem.LASTDEACTIVATEDATE = export_user.LastDeActivateDate.Year > 2000 ? export_user.LastDeActivateDate.ToddMMyyyy() : "N/A";
                exportItem.LASTSUSPENDDATE = export_user.LastSuspendDate.Year > 2000 ? export_user.LastSuspendDate.ToddMMyyyy() : "N/A";
                exportItem.LASTLOGINDATE = export_user.DateLastLogin.Year > 2000 ? export_user.DateLastLogin.ToddMMyyyy() : "N/A";

                //exportItem.LASTLOGINDATE = export_user.DateLastLogin.DateTime != DateTime.MinValue ? export_user.DateLastLogin.ToddMMyyyy() : "N/A";
                //exportItem.LASTSUSPENDDATE = export_user.LastSuspendDate.Year > 2000 ? export_user.LastSuspendDate.ToddMMyyyy() : "N/A";
                //exportItem.LASTDEACTIVATEDATE = export_user.LastDeActivateDate.Year > 2000 ? export_user.LastDeActivateDate.ToddMMyyyy() : "N/A";

                exportItem.STARTDATE = export_user.CreatedAt.ToddMMyyyy();
                exportItem.ENDDATE = exportItem.LASTDEACTIVATEDATE;

                list.Add(exportItem);


            }

            StringWriter sw = new StringWriter();
            var csv = new CsvWriter(sw);
            csv.Configuration.RegisterClassMap<NparksUserExportCSVMapper>();
            csv.Configuration.Encoding = Encoding.UTF8;
            csv.Configuration.Delimiter = "|";
            csv.WriteHeader<UserViewModel.NparksUserExport_ACE_CSVViewModel>();
            csv.WriteRecords(list);
            //return sw.ToString();
            return GetBytes(sw.ToString());
        }*/

        //public byte[] ExportNparksUsersInfoForAudit()
        //{
        //    var activeUsers = GetUsers().ToList();

        //    var list = new List<UserViewModel.NparksUserExportCSVViewModel>();
        //    foreach (var activeUser in activeUsers)
        //    {
        //        //var roles = userManager.GetRoles(activeUser.Id);

        //        var exportItem = new UserViewModel.NparksUserExportCSVViewModel();
        //        var logins =   userManager.GetLogins(activeUser.Id);
        //        var groupname = "";
        //        if (activeUser.Group != null)
        //            groupname = activeUser.Group.Name.ToLower();
        //            if (logins.Count > 0)
        //        {
        //           if (groupname != BIOME.Constants.Account.UserGroup.Public.ToLower())
        //            {

        //                exportItem.SOEID = logins.FirstOrDefault().ProviderKey;

        //                exportItem.STAFFNAME = activeUser.FullName;
        //                exportItem.ACCSTATUS =(activeUser.IsActive ? BIOME.Constants.Account.AccountStatus.ACTIVE:   ( (activeUser.AccountStatus == BIOME.Constants.Account.AccountStatus.DEACTIVATED ? activeUser.AccountStatus : BIOME.Constants.Account.AccountStatus.SUSPENDED)));
        //                exportItem.EMAIL = activeUser.Email;
        //                exportItem.LASTDEACTIVATEDATE = activeUser.LastDeActivateDate.Year > 2000 ? activeUser.LastDeActivateDate.ToString("dd/MM/yyyy HH:mm:ss") : "NIL";
        //                exportItem.LASTSUSPENDDATE = activeUser.LastSuspendDate.Year > 2000 ? activeUser.LastSuspendDate.ToString("dd/MM/yyyy HH:mm:ss") : "NIL";
        //                exportItem.LASTLOGINDATE = activeUser.DateLastLogin.Year > 2000 ? activeUser.DateLastLogin.ToString("dd/MM/yyyy HH:mm:ss") : "NIL";
        //                exportItem.ENDDATE = "";
        //                exportItem.ROLECODE = exportItem.ROLEDESC = String.Join(" ", GetRolesForUser(activeUser.Id).ToList().ToArray());// GetRolesForUser(activeUser.Id).ToList();
        //                exportItem.STARTDATE = "";
        //                exportItem.USERID = logins.FirstOrDefault().ProviderKey;


        //                list.Add(exportItem);
        //            }
        //        }
        //    }

        //    StringWriter sw = new StringWriter();
        //    var csv = new CsvWriter(sw);
        //    csv.Configuration.RegisterClassMap<NparksUserExportCSVMapper>();
        //    csv.WriteHeader<UserViewModel.NparksUserExportCSVViewModel>();
        //    csv.WriteRecords(list);
        //    return GetBytes(sw.ToString());
        //}
        #endregion

        #region Private Methods

        static byte[] GetBytes(string str)
        {
            byte[] bytes = new byte[str.Length * sizeof(char)];
            System.Buffer.BlockCopy(str.ToCharArray(), 0, bytes, 0, bytes.Length);
            return bytes;
        }

        static string GetString(byte[] bytes)
        {
            char[] chars = new char[bytes.Length / sizeof(char)];
            System.Buffer.BlockCopy(bytes, 0, chars, 0, bytes.Length);
            return new string(chars);
        }

        public static ServiceResult GenerateUserViewUseableErrors(string email, string username, IEnumerable<string> errors)
        {
            return GenerateUserViewUseableErrors(email, username, errors.ToArray());
        }

        public static ServiceResult GenerateUserViewUseableErrors(string email, string username, params string[] errors)
        {
            List<ServiceError> serviceErrors = new List<ServiceError>();
            foreach (var error in errors)
            {
                if (error == string.Format("Email '{0}' is already taken.", email))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1005, ModelPropertyName = "Email", Parameters = new List<string>() { email } });
                }
                else if (error == string.Format("Email '{0}' is invalid.", email))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1004, ModelPropertyName = "Email", Parameters = new List<string>() { email } });
                }
                else if (error == string.Format("Name {0} is already taken.", username))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1006, ModelPropertyName = "UserName", Parameters = new List<string>() { username } });
                }
                else if (error == string.Format("User name {0} is invalid, can only contain letters or digits.", username))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1007, ModelPropertyName = "UserName", Parameters = new List<string>() { username } });
                }
                else if (error == "Incorrect password.") {
                    serviceErrors.Add(new ServiceError() { Code = 1011, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1011, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "Passwords must have at least one digit ('0'-'9').") {
                    serviceErrors.Add(new ServiceError() { Code = 1012, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1012, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "Passwords must have at least one lowercase ('a'-'z').") {
                    serviceErrors.Add(new ServiceError() { Code = 1013, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1013, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "Passwords must have at least one non letter or digit character.")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1014, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1014, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "Passwords must have at least one uppercase ('A'-'Z').")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1015, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1015, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "Passwords must be at least 8 characters.")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1016, ModelPropertyName = "Password" });
                    serviceErrors.Add(new ServiceError() { Code = 1016, ModelPropertyName = "ReTypePassword" });
                }
                else if (error == "User already in role.")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1017, ModelPropertyName = "RoleID" });
                }
                else if (error == "Cannot reuse old password")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1025, ModelPropertyName = "Password", Parameters = new List<string>() { Constants.Account.Password.PasswordHistoryLimit.ToString() } });
                }
                else if (error == "Invalid token.")
                {
                    serviceErrors.Add(new ServiceError() { Code = 1033 });
                }
                else
                {
                    serviceErrors.Add(new ServiceError() { Code = 1000 });
                }
            }

            return ServiceResult.Failed(serviceErrors.ToArray());
        }

        public static ServiceResult GenerateRoleViewUsableErrors(string rolename, IEnumerable<string> errors)
        {
            return GenerateRoleViewUsableErrors(rolename, errors.ToArray());
        }

        public static ServiceResult GenerateRoleViewUsableErrors(string rolename, params string[] errors)
        {
            List<ServiceError> serviceErrors = new List<ServiceError>();
            foreach (var error in errors)
            {
                if (error == string.Format("Name {0} is already taken.", rolename))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1018, ModelPropertyName = "RoleName", Parameters = new List<string>() { rolename } });
                }
                else if (error == string.Format("{0} cannot be null or empty.", rolename))
                {
                    serviceErrors.Add(new ServiceError() { Code = 1019, ModelPropertyName = "RoleName", Parameters = new List<string>() { rolename } });
                }
                else
                {
                    serviceErrors.Add(new ServiceError() { Code = 1000 });
                }
            }

            return ServiceResult.Failed(serviceErrors.ToArray());
        }


        public string GetIP(bool CheckForward = false)
        {
            string ip = null;
            if (CheckForward)
            {
                ip = HttpContext.Current.Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
            }

            if (string.IsNullOrEmpty(ip))
            {
                ip = HttpContext.Current.Request.ServerVariables["REMOTE_ADDR"];
            }
            else
            { // Using X-Forwarded-For last address
                /*ip = ip.Split(',')
                       .Last()
                       .Trim();*/

                //Use client IP. Issue ID: NPARKS-41
                ip = ip.Split(',')
                       .First()
                       .Trim();
            }

            return ip;
        }
        public void SendTestingEmail(string to, string subject)
        {
            emailService.SendTestingEmail(to, subject);

        }
        public string ConstructHMAC(string value, string developerId)
        {
             return developerInfoService.ConstructDeveloperTokenHash(value, developerId);
        }

        public string ConstructHashForLoginFormRandomRoute(string input)
        {
            return developerInfoService.ConstructHashForLoginFormRandomRoute(input);
        }
        public string GenerateRandomFormRoute()
        {
            return developerInfoService.GenerateRandomFormRoute();
        }

        #endregion
    }

    public static class UserServiceExtensions
    {
        public static ApplicationUser GetUserById(this IUserQueryService userService, long id)
        {
            if (userService == null)
            {
                throw new ArgumentNullException(nameof(userService));
            }
            return AsyncHelper.RunSync(() => userService.GetUserByIdAsync(id));
        }
        public static ApplicationUser GetUserbyEmail(this IUserQueryService userService, string email)
        {
            if (userService == null)
            {
                throw new ArgumentNullException(nameof(userService));
            }
            return AsyncHelper.RunSync(() => userService.GetUserbyEmailAsync(email));
        }
     

        public static bool HasPasswordExpired(this IUserService userService, long userId)
        {
            if (userService == null)
            {
                throw new ArgumentNullException(nameof(userService));
            }
            return AsyncHelper.RunSync(() => userService.HasPasswordExpiredAsync(userId));
        }

        public static string ToProperRoleName(this string dbRoleName)
        {
            var roleName = "";
            switch (dbRoleName)
            {
                case UserRoles.SystemAdmin:
                    roleName = "System Admin";
                    break;
                case UserRoles.PermitManager:
                    roleName = "Permit Manager";
                    break;
                case UserRoles.SiteManager:
                    roleName = "Site Manager";
                    break;
                case UserRoles.Expert:
                    roleName = "Expert";
                    break;
                case UserRoles.ResourceUploader:
                    roleName = "Resource Uploader";
                    break;
                case UserRoles.ProjectAdmin:
                    roleName = "Project Admin";
                    break;
                case UserRoles.ProjectMember:
                    roleName = "Project Member";
                    break;
                case UserRoles.Public:
                    roleName = "General"; //Public
                    break;
                default:
                    break;
            }

            return roleName;
        }

        public static string ToProperRoleName(this long roleId)
        {
            var roleName = "";
            switch (roleId)
            {
                case UserRoles.SystemAdminId:
                    roleName = "System Admin";
                    break;
                case UserRoles.PermitManagerId:
                    roleName = "Permit Manager";
                    break;
                case UserRoles.SiteManagerId:
                    roleName = "Site Manager";
                    break;
                case UserRoles.ExpertId:
                    roleName = "Expert";
                    break;
                case UserRoles.ResourceUploaderId:
                    roleName = "Resource Uploader";
                    break;
                case UserRoles.ProjectAdminId:
                    roleName = "Project Admin";
                    break;
                case UserRoles.ProjectMemberId:
                    roleName = "Project Member";
                    break;
                case UserRoles.PublicId:
                    roleName = "General"; //Public
                    break;
                default:
                    break;
            }

            return roleName;
        }

        public static ServiceResult SetUserBlacklistedState(this IUserService userService, long userId, bool isBlacklisted)
        {
            if (userService == null)
            {
                throw new ArgumentNullException(nameof(userService));
            }
            return AsyncHelper.RunSync(() => userService.SetUserBlacklistedStateAsync(userId, isBlacklisted));
        }
       
    }
    public static class ExtensionMethods
    {
        public static string ToddMMyyyy(this DateTimeOffset dt)
        {
            return dt.ToString("dd/MM/yyyy");
        }
        public static string ToddMMyyyyHHmmss(this DateTimeOffset dt)
        {
            return dt.ToString("dd/MM/yyyy HH:mm:ss");
        }
        public static string ToddMMyyyyhhmmsstt(this DateTimeOffset dt)
        {
            return dt.ToString("dd/MM/yyyy hh:mm:ss tt");
        }
        public static string ToddMMMyyyy(this DateTimeOffset dt)
        {
            return dt.ToString("dd-MMM-yyyy");
        }
    }
}
